
0,<PERSON><PERSON> Jul 08 19:17:59 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



0,Tue Jul 08 19:18:01 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



0,Tue Jul 08 19:18:02 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



0,Tue Jul 08 19:18:05 EDT 2025*PRE_OUTPUT*
>>Running suite A2Observables
<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
>>Running test LocatablePropertyChangeListenersProperty
<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
*END_OUTPUT*



0,Tue Jul 08 19:18:06 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
>>Running test BridgeSceneScrollMethodDefined
<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
>>Running test BridgeSceneGalahadScrollLeftArmTestCase
<<
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
>>Running test BridgeSceneArthurScrollRightLegTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
*END_OUTPUT*



0,Tue Jul 08 19:18:07 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:34 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:35 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:37 EDT 2025*PREVIOUS_OUTPUT*


1,Tue Jul 08 20:25:39 EDT 2025*PREVIOUS_OUTPUT*


1,Tue Jul 08 20:25:40 EDT 2025*PREVIOUS_OUTPUT*


1,Tue Jul 08 20:25:40 EDT 2025*PREVIOUS_OUTPUT*


1,Tue Jul 08 20:25:47 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
>>Running test BridgeSceneScrollMethodDefined
<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
>>Running test BridgeSceneGalahadScrollLeftArmTestCase
<<
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
>>Running test BridgeSceneArthurScrollRightLegTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:48 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:50 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:51 EDT 2025*PRE_OUTPUT*
>>Running suite A2Observables
<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
>>Running test LocatablePropertyChangeListenersProperty
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:53 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
>>Running test BridgeSceneSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:54 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:25:59 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:26:00 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



1,Tue Jul 08 20:26:02 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



2,Tue Jul 08 22:18:48 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



2,Tue Jul 08 22:18:51 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



2,Tue Jul 08 22:18:52 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



2,Tue Jul 08 22:18:53 EDT 2025*PRE_OUTPUT*
>>Running suite A2Observables
<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
>>Running test LocatablePropertyChangeListenersProperty
<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
*END_OUTPUT*



2,Tue Jul 08 22:18:54 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
>>Running test BridgeSceneScrollMethodDefined
<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
>>Running test BridgeSceneGalahadScrollLeftArmTestCase
<<
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
>>Running test BridgeSceneArthurScrollRightLegTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



2,Tue Jul 08 22:18:56 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,0.0% complete,0.0,0.0,No checkstyle output, check console error messages
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



3,Tue Jul 08 22:19:03 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



4,Tue Jul 08 22:20:15 EDT 2025*PREVIOUS_OUTPUT*


5,Tue Jul 08 22:21:16 EDT 2025*PREVIOUS_OUTPUT*


6,Tue Jul 08 22:22:22 EDT 2025*PREVIOUS_OUTPUT*


7,Tue Jul 08 22:25:47 EDT 2025*PREVIOUS_OUTPUT*


7,Tue Jul 08 22:25:56 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



8,Tue Jul 08 22:29:09 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



9,Tue Jul 08 22:30:08 EDT 2025*PREVIOUS_OUTPUT*


9,Tue Jul 08 22:30:09 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



10,Tue Jul 08 22:32:17 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



10,Tue Jul 08 22:32:20 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



11,Tue Jul 08 22:33:06 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



11,Tue Jul 08 22:33:07 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



11,Tue Jul 08 22:33:08 EDT 2025*PREVIOUS_OUTPUT*


12,Tue Jul 08 22:33:23 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



13,Tue Jul 08 22:34:58 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:16: Missing signature passed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:16: Missing signature failed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeScenePassedMethodDefined failed.
Please correct the problems identified by preceding test:BridgeScenePassedMethodDefined before running this test
<<
*END_OUTPUT*



14,Tue Jul 08 23:04:20 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Method matching approach not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



15,Tue Jul 08 23:06:59 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Method matching getOccupied not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



16,Tue Jul 08 23:08:51 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Method matching getKnightTurn not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



17,Tue Jul 08 23:09:41 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,85.00000000000001% complete,42.5,50.0,Avatar(images/galahad.jpg) said  "Hi" instead of "Grail"
<<
*END_OUTPUT*



18,Tue Jul 08 23:10:43 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



19,Tue Jul 08 23:11:20 EDT 2025*PREVIOUS_OUTPUT*


19,Tue Jul 08 23:11:27 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\ArthurHead.java:20:41: 'fn' hides a field. [HiddenField]mp\bridge\ArthurHead.java:28:26: 'x' hides a field. [HiddenField]mp\bridge\ArthurHead.java:36:26: 'y' hides a field. [HiddenField]mp\bridge\AvatarImpl.java:15:34: 'head' hides a field. [HiddenField]mp\bridge\GalahadHead.java:21:41: 'fn' hides a field. [HiddenField]mp\bridge\GalahadHead.java:29:26: 'x' hides a field. [HiddenField]mp\bridge\GalahadHead.java:37:26: 'y' hides a field. [HiddenField]mp\bridge\GuardHead.java:20:41: 'fn' hides a field. [HiddenField]mp\bridge\GuardHead.java:28:26: 'x' hides a field. [HiddenField]mp\bridge\GuardHead.java:36:26: 'y' hides a field. [HiddenField]mp\bridge\LancelotHead.java:21:41: 'fn' hides a field. [HiddenField]mp\bridge\LancelotHead.java:29:26: 'x' hides a field. [HiddenField]mp\bridge\LancelotHead.java:37:26: 'y' hides a field. [HiddenField]mp\bridge\RobinHead.java:20:41: 'fn' hides a field. [HiddenField]mp\bridge\RobinHead.java:28:26: 'x' hides a field. [HiddenField]mp\bridge\RobinHead.java:36:26: 'y' hides a field. [HiddenField]mp\bridge\SpeechBubble.java:25:26: 'x' hides a field. [HiddenField]mp\bridge\SpeechBubble.java:32:26: 'y' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:20:29: Name 'some_x' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:21:29: Name 'some_y' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:22:29: Name 'lance_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:23:29: Name 'robin_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:24:29: Name 'gal_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:25:29: Name 'guard_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:30:21: Name 'KnightTurn' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:36:21: Name 'Occupied' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:37:30: Name 'Gorge_X' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:38:24: Name 'Gorge_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:10:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:11:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:12:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,90.47619047619048% complete,4.5,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,80.0% complete,8.0,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,76.92307692307693% complete,3.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,71.36929460580913% complete,7.1,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,42.5531914893617% complete,3.0,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



20,Tue Jul 08 23:11:40 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



20,Tue Jul 08 23:11:45 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



21,Tue Jul 08 23:33:23 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



22,Wed Jul 09 00:02:11 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



23,Wed Jul 09 00:17:17 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:6: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



24,Wed Jul 09 00:24:38 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:7: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



25,Wed Jul 09 00:26:04 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) (.*)!addPropertyChangeListener:PropertyChangeListener->void(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) java\.io\.PrintStream!println:\*->\.\*(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test ConsoleSceneViewSingletonFromFactory failed.
Please correct the problems identified by preceding test:ConsoleSceneViewSingletonFromFactory before running this test
<<
*END_OUTPUT*



25,Wed Jul 09 00:26:07 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) (.*)!addPropertyChangeListener:PropertyChangeListener->void(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) java\.io\.PrintStream!println:\*->\.\*(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test ConsoleSceneViewSingletonFromFactory failed.
Please correct the problems identified by preceding test:ConsoleSceneViewSingletonFromFactory before running this test
<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:7: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



25,Wed Jul 09 00:26:12 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) (.*)!addPropertyChangeListener:PropertyChangeListener->void(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) java\.io\.PrintStream!println:\*->\.\*(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test ConsoleSceneViewSingletonFromFactory failed.
Please correct the problems identified by preceding test:ConsoleSceneViewSingletonFromFactory before running this test
<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:7: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



26,Wed Jul 09 00:56:15 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



26,Wed Jul 09 00:56:32 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



27,Wed Jul 09 00:57:24 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



27,Wed Jul 09 00:57:40 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



27,Wed Jul 09 00:57:43 EDT 2025*PREVIOUS_OUTPUT*


28,Wed Jul 09 00:58:35 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns instance of class main.BridgeSceneImpl instead of class main.Assignment1
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



28,Wed Jul 09 00:58:47 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns instance of class main.BridgeSceneImpl instead of class main.Assignment1
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



29,Wed Jul 09 00:59:27 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



29,Wed Jul 09 00:59:32 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



29,Wed Jul 09 00:59:55 EDT 2025*PREVIOUS_OUTPUT*


29,Wed Jul 09 00:59:59 EDT 2025*PREVIOUS_OUTPUT*


29,Wed Jul 09 01:00:01 EDT 2025*PREVIOUS_OUTPUT*


30,Wed Jul 09 01:01:09 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



31,Wed Jul 09 01:01:25 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



32,Wed Jul 09 01:02:12 EDT 2025*PREVIOUS_OUTPUT*


32,Wed Jul 09 01:02:15 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



32,Wed Jul 09 01:02:19 EDT 2025*PREVIOUS_OUTPUT*


33,Wed Jul 09 01:20:54 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



33,Wed Jul 09 01:21:01 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



34,Wed Jul 09 01:22:02 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



35,Wed Jul 09 01:23:18 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



35,Wed Jul 09 01:23:48 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*main.Assignment2(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags\.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



36,Wed Jul 09 01:25:26 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\AvatarImpl.java:1: No method in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



37,Wed Jul 09 01:30:43 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



38,Wed Jul 09 01:31:38 EDT 2025*PREVIOUS_OUTPUT*


39,Wed Jul 09 01:33:13 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



40,Wed Jul 09 01:34:46 EDT 2025*PREVIOUS_OUTPUT*


41,Wed Jul 09 01:36:04 EDT 2025*PREVIOUS_OUTPUT*


42,Wed Jul 09 01:37:53 EDT 2025*PREVIOUS_OUTPUT*


43,Wed Jul 09 01:39:55 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
*END_OUTPUT*



44,Wed Jul 09 01:41:22 EDT 2025*PREVIOUS_OUTPUT*


44,Wed Jul 09 01:41:25 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
*END_OUTPUT*



45,Wed Jul 09 01:43:21 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
*END_OUTPUT*



46,Wed Jul 09 01:44:49 EDT 2025*PREVIOUS_OUTPUT*


47,Wed Jul 09 01:45:48 EDT 2025*PREVIOUS_OUTPUT*


48,Wed Jul 09 01:48:45 EDT 2025*PREVIOUS_OUTPUT*


48,Wed Jul 09 01:48:47 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
*END_OUTPUT*



49,Wed Jul 09 01:52:06 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
*END_OUTPUT*



50,Wed Jul 09 01:53:48 EDT 2025*PREVIOUS_OUTPUT*


51,Wed Jul 09 01:54:08 EDT 2025*PREVIOUS_OUTPUT*


51,Wed Jul 09 01:54:56 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.shapes.RotatingLine with tag Locatable has untagged interface interface util.models.PropertyListenerRegisterer
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
*END_OUTPUT*



51,Wed Jul 09 01:55:01 EDT 2025*PRE_OUTPUT*
>>Running suite A2Observables
<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
>>Running test LocatablePropertyChangeListenersProperty
<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.shapes.RotatingLine with tag Locatable has untagged interface interface util.models.PropertyListenerRegisterer
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
*END_OUTPUT*



51,Wed Jul 09 01:55:03 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
>>Running test BridgeSceneScrollMethodDefined
<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
>>Running test BridgeSceneGalahadScrollLeftArmTestCase
<<
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
>>Running test BridgeSceneArthurScrollRightLegTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.shapes.RotatingLine with tag Locatable has untagged interface interface util.models.PropertyListenerRegisterer
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*@scroll:int;int->void//EC.*Comp301Tags\.BRIDGE_SCENE.*\[ExpectedSignatures\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



52,Wed Jul 09 01:55:22 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.shapes.RotatingLine with tag Locatable has untagged interface interface util.models.PropertyListenerRegisterer
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
*END_OUTPUT*



53,Wed Jul 09 01:58:31 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) (.*)!addPropertyChangeListener:PropertyChangeListener->void(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) java\.io\.PrintStream!println:\*->\.\*(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
*END_OUTPUT*



54,Wed Jul 09 02:06:24 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) (.*)!addPropertyChangeListener:PropertyChangeListener->void(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.CONSOLE_SCENE_VIEW(.*) java\.io\.PrintStream!println:\*->\.\*(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test ConsoleSceneViewSingletonFromFactory failed.
Please correct the problems identified by preceding test:ConsoleSceneViewSingletonFromFactory before running this test
<<
*END_OUTPUT*



55,Wed Jul 09 02:09:50 EDT 2025*PREVIOUS_OUTPUT*


56,Wed Jul 09 02:11:05 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneView,15.0% complete,7.5,50.0,No of unique event sources < 7. No new value before fail is an old value after fail. 
<<
*END_OUTPUT*



57,Wed Jul 09 02:12:04 EDT 2025*PREVIOUS_OUTPUT*


58,Wed Jul 09 02:13:57 EDT 2025*PREVIOUS_OUTPUT*


59,Wed Jul 09 02:15:21 EDT 2025*PREVIOUS_OUTPUT*


59,Wed Jul 09 02:15:28 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneView,15.0% complete,7.5,50.0,No of unique event sources < 7. No new value before fail is an old value after fail. 
<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
*END_OUTPUT*



59,Wed Jul 09 02:15:43 EDT 2025*PRE_OUTPUT*
>>Running suite A2Observables
<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
>>Running test LocatablePropertyChangeListenersProperty
<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneView,15.0% complete,7.5,50.0,No of unique event sources < 7. No new value before fail is an old value after fail. 
<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*java.beans.PropertyChangeEvent.*Comp301Tags\.BOUNDED_SHAPE.*.*\[ExpectedClassInstantiations\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.BOUNDED_SHAPE(.*) (.*)!(.*):java.beans.PropertyChangeEvent->(.*)(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
*END_OUTPUT*



59,Wed Jul 09 02:15:51 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,100.0% complete,0.0,0.0,
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneView,15.0% complete,7.5,50.0,No of unique event sources < 7. No new value before fail is an old value after fail. 
<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*: .*@Comp301Tags\.AVATAR(.*) @Comp301Tags\.FACTORY_CLASS!@legsFactoryMethod:\*->@Comp301Tags\.ANGLE(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,100.0% complete,0.0,0.0,
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*java.beans.PropertyChangeEvent.*Comp301Tags\.BOUNDED_SHAPE.*.*\[ExpectedClassInstantiations\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,Checkstyle output does not match:.*INFO.*: .*Comp301Tags\.BOUNDED_SHAPE(.*) (.*)!(.*):java.beans.PropertyChangeEvent->(.*)(.*)\[MissingMethodCall\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



60,Wed Jul 09 02:16:06 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



61,Wed Jul 09 02:17:22 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape does not have an interface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



61,Wed Jul 09 02:17:29 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape does not have an interface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



62,Wed Jul 09 02:19:07 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape does not have an interface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



62,Wed Jul 09 02:19:10 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape does not have an interface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



63,Wed Jul 09 02:19:43 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape has untagged interface interface mp.shapes.AScalableRectangleInterface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



63,Wed Jul 09 02:19:44 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape has untagged interface interface mp.shapes.AScalableRectangleInterface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



64,Wed Jul 09 02:20:10 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,100.0% complete,0.0,0.0,
<<
>>Test Result:
BoundedShapeWidthProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeWidthEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



64,Wed Jul 09 02:20:50 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,100.0% complete,0.0,0.0,
<<
>>Test Result:
BoundedShapeWidthProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeWidthEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



65,Wed Jul 09 02:21:51 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,100.0% complete,0.0,0.0,
<<
>>Test Result:
BoundedShapeWidthProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeWidthEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



66,Wed Jul 09 02:22:39 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



66,Wed Jul 09 02:22:44 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



67,Wed Jul 09 02:23:00 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,100.0% complete,0.0,0.0,
<<
>>Test Result:
BoundedShapeWidthProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeWidthEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
BoundedShapeHeightEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



68,Wed Jul 09 03:21:42 EDT 2025*PREVIOUS_OUTPUT*


69,Wed Jul 09 03:23:21 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



70,Wed Jul 09 03:31:37 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatableXProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableXEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
LocatableYEditableProperty,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape has untagged interface interface mp.shapes.AScalableRectangleInterface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test BoundedShapeWidthProperty failed.
Please correct the problems identified by preceding test:BoundedShapeWidthProperty before running this test
<<
*END_OUTPUT*



71,Wed Jul 09 03:32:33 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*approach:@Comp301Tags\.AVATAR->void.*Comp301Tags\.BRIDGE_SCENE.*\[ExpectedSignatures\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*say:String->void.*Comp301Tags\.BRIDGE_SCENE.*\[ExpectedSignatures\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*passed:->void.*Comp301Tags\.BRIDGE_SCENE.*\[ExpectedSignatures\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*failed:->void.*Comp301Tags\.BRIDGE_SCENE.*\[ExpectedSignatures\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



72,Wed Jul 09 03:32:53 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,Checkstyle output does not match:.*INFO.*approach:@Comp301Tags\.AVATAR->void.*Comp301Tags\.BRIDGE_SCENE.*\[ExpectedSignatures\].*. Is your class named or tagged properly and checkstyle file upto date?
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



73,Wed Jul 09 03:34:41 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



74,Wed Jul 09 03:37:03 EDT 2025*PREVIOUS_OUTPUT*


74,Wed Jul 09 03:37:07 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



74,Wed Jul 09 03:37:11 EDT 2025*PREVIOUS_OUTPUT*


75,Wed Jul 09 03:38:20 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



75,Wed Jul 09 03:38:21 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



76,Wed Jul 09 03:38:57 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



76,Wed Jul 09 03:38:59 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



77,Wed Jul 09 03:40:15 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
*END_OUTPUT*



77,Wed Jul 09 03:40:18 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



77,Wed Jul 09 03:40:23 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,main\StaticFactoryClass.java:5: Missing signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
<<
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:10: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



78,Wed Jul 09 03:41:24 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:9: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



79,Wed Jul 09 03:42:07 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:9: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



80,Wed Jul 09 03:43:50 EDT 2025*PREVIOUS_OUTPUT*


80,Wed Jul 09 03:43:54 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:9: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



81,Wed Jul 09 03:44:12 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:9: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



82,Wed Jul 09 03:48:07 EDT 2025*PREVIOUS_OUTPUT*


83,Wed Jul 09 03:49:38 EDT 2025*PREVIOUS_OUTPUT*


84,Wed Jul 09 03:53:29 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:7: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



85,Wed Jul 09 03:56:51 EDT 2025*PREVIOUS_OUTPUT*


86,Wed Jul 09 04:00:23 EDT 2025*PREVIOUS_OUTPUT*


87,Wed Jul 09 04:04:52 EDT 2025*PREVIOUS_OUTPUT*


88,Wed Jul 09 04:08:01 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:6: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



89,Wed Jul 09 04:08:13 EDT 2025*PREVIOUS_OUTPUT*


90,Wed Jul 09 04:11:39 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Method matching approach not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



91,Wed Jul 09 04:13:40 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Method matching getOccupied not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



92,Wed Jul 09 04:15:00 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Method matching getKnightTurn not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



93,Wed Jul 09 04:16:17 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



94,Wed Jul 09 04:17:28 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:6: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



95,Wed Jul 09 04:18:04 EDT 2025*PREVIOUS_OUTPUT*


95,Wed Jul 09 04:18:04 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:6: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



96,Wed Jul 09 04:18:22 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



97,Wed Jul 09 04:18:37 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:9: Missing signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



98,Wed Jul 09 04:18:53 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



99,Wed Jul 09 04:21:07 EDT 2025*PREVIOUS_OUTPUT*


99,Wed Jul 09 04:21:17 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\ArthurHead.java:16:41: 'fn' hides a field. [HiddenField]mp\bridge\ArthurHead.java:24:26: 'x' hides a field. [HiddenField]mp\bridge\ArthurHead.java:32:26: 'y' hides a field. [HiddenField]mp\bridge\AvatarImpl.java:14:34: 'head' hides a field. [HiddenField]mp\bridge\GalahadHead.java:16:41: 'fn' hides a field. [HiddenField]mp\bridge\GalahadHead.java:24:26: 'x' hides a field. [HiddenField]mp\bridge\GalahadHead.java:32:26: 'y' hides a field. [HiddenField]mp\bridge\GuardHead.java:17:41: 'fn' hides a field. [HiddenField]mp\bridge\GuardHead.java:25:26: 'x' hides a field. [HiddenField]mp\bridge\GuardHead.java:33:26: 'y' hides a field. [HiddenField]mp\bridge\LancelotHead.java:19:41: 'fn' hides a field. [HiddenField]mp\bridge\LancelotHead.java:26:26: 'x' hides a field. [HiddenField]mp\bridge\LancelotHead.java:35:26: 'y' hides a field. [HiddenField]mp\bridge\RobinHead.java:19:41: 'fn' hides a field. [HiddenField]mp\bridge\RobinHead.java:27:26: 'x' hides a field. [HiddenField]mp\bridge\RobinHead.java:35:26: 'y' hides a field. [HiddenField]mp\bridge\SpeechBubble.java:25:26: 'x' hides a field. [HiddenField]mp\bridge\SpeechBubble.java:32:26: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:9:39: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:9:46: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:9:53: 'width' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:9:64: 'height' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:20:29: Name 'some_x' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:21:29: Name 'some_y' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:22:29: Name 'lance_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:23:29: Name 'robin_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:24:29: Name 'gal_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:25:29: Name 'guard_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:30:21: Name 'KnightTurn' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:36:21: Name 'Occupied' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:37:30: Name 'Gorge_X' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:38:24: Name 'Gorge_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,86.04651162790698% complete,4.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,80.0% complete,8.0,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,main\Assignment2.java:3:17: Using the '.*' form of import should be avoided - bus.uigen.*. [AvoidStarImport]main\Assignment2.java:4:17: Using the '.*' form of import should be avoided - mp.shapes.*. [AvoidStarImport]main\Assignment2.java:5:17: Using the '.*' form of import should be avoided - mp.bridge.*. [AvoidStarImport]main\StaticFactoryClass.java:2:17: Using the '.*' form of import should be avoided - mp.bridge.*. [AvoidStarImport]mp\bridge\Angle.java:2:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\Angle.java:4:17: Using the '.*' form of import should be avoided - mp.shapes.*. [AvoidStarImport]mp\bridge\ArthurHead.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\Avatar.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\AvatarImpl.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\BridgeScene.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\GalahadHead.java:2:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\GuardHead.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\ImageShape.java:2:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\SpeechBubble.java:4:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\StringShape.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\bridge\VShape.java:2:17: Using the '.*' form of import should be avoided - mp.shapes.*. [AvoidStarImport]mp\bridge\VShape.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\shapes\RotateLine.java:3:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]mp\shapes\RotatingLine.java:2:24: Using the '.*' form of import should be avoided - util.annotations.*. [AvoidStarImport]
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,72.54901960784314% complete,7.3,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,41.95804195804196% complete,2.9,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



100,Wed Jul 09 05:00:04 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\AvatarImpl.java:16:34: 'head' hides a field. [HiddenField]mp\bridge\LancelotHead.java:19:41: 'fn' hides a field. [HiddenField]mp\bridge\LancelotHead.java:26:26: 'x' hides a field. [HiddenField]mp\bridge\LancelotHead.java:35:26: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:39: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:46: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:53: 'width' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:64: 'height' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:20:29: Name 'some_x' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:21:29: Name 'some_y' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:22:29: Name 'lance_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:23:29: Name 'robin_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:24:29: Name 'gal_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:25:29: Name 'guard_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:30:21: Name 'KnightTurn' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:36:21: Name 'Occupied' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:37:30: Name 'Gorge_X' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:38:24: Name 'Gorge_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,87.75510204081633% complete,4.4,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,main\Assignment2.java:3:17: Using the '.*' form of import should be avoided - bus.uigen.*. [AvoidStarImport]main\Assignment2.java:4:17: Using the '.*' form of import should be avoided - mp.shapes.*. [AvoidStarImport]main\Assignment2.java:5:17: Using the '.*' form of import should be avoided - mp.bridge.*. [AvoidStarImport]main\StaticFactoryClass.java:2:17: Using the '.*' form of import should be avoided - mp.bridge.*. [AvoidStarImport]
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,69.6551724137931% complete,7.0,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,42.038216560509554% complete,2.9,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



100,Wed Jul 09 05:00:14 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\AvatarImpl.java:16:34: 'head' hides a field. [HiddenField]mp\bridge\LancelotHead.java:19:41: 'fn' hides a field. [HiddenField]mp\bridge\LancelotHead.java:26:26: 'x' hides a field. [HiddenField]mp\bridge\LancelotHead.java:35:26: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:39: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:46: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:53: 'width' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:64: 'height' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:20:29: Name 'some_x' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:21:29: Name 'some_y' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:22:29: Name 'lance_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:23:29: Name 'robin_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:24:29: Name 'gal_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:25:29: Name 'guard_const' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:30:21: Name 'KnightTurn' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:36:21: Name 'Occupied' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]main\BridgeSceneImpl.java:37:30: Name 'Gorge_X' must match pattern '^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$'. [ConstantName]main\BridgeSceneImpl.java:38:24: Name 'Gorge_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,87.75510204081633% complete,4.4,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,main\Assignment2.java:3:17: Using the '.*' form of import should be avoided - bus.uigen.*. [AvoidStarImport]main\Assignment2.java:4:17: Using the '.*' form of import should be avoided - mp.shapes.*. [AvoidStarImport]main\Assignment2.java:5:17: Using the '.*' form of import should be avoided - mp.bridge.*. [AvoidStarImport]main\StaticFactoryClass.java:2:17: Using the '.*' form of import should be avoided - mp.bridge.*. [AvoidStarImport]
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,69.6551724137931% complete,7.0,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,42.038216560509554% complete,2.9,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



101,Wed Jul 09 05:22:37 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\AvatarImpl.java:16:40: 'head' hides a field. [HiddenField]mp\bridge\LancelotHead.java:19:41: 'fn' hides a field. [HiddenField]mp\bridge\LancelotHead.java:26:26: 'x' hides a field. [HiddenField]mp\bridge\LancelotHead.java:35:26: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:39: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:46: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:53: 'width' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:64: 'height' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:39:24: Name 'GORGE_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,68.02721088435374% complete,6.8,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



101,Wed Jul 09 05:22:40 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\AvatarImpl.java:16:40: 'head' hides a field. [HiddenField]mp\bridge\LancelotHead.java:19:41: 'fn' hides a field. [HiddenField]mp\bridge\LancelotHead.java:26:26: 'x' hides a field. [HiddenField]mp\bridge\LancelotHead.java:35:26: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:39: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:46: 'y' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:53: 'width' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:10:64: 'height' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:39:24: Name 'GORGE_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,68.02721088435374% complete,6.8,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



102,Wed Jul 09 05:24:52 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:39:24: Name 'GORGE_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,67.55852842809364% complete,6.8,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



102,Wed Jul 09 05:25:31 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,main\BridgeSceneImpl.java:39:24: Name 'GORGE_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [StaticVariableName]mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,67.55852842809364% complete,6.8,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



103,Wed Jul 09 05:29:48 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,67.11409395973155% complete,6.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



103,Wed Jul 09 05:29:51 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,mp\shapes\Gorge.java:4:9: Name 'RIGHT_LINE_X' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:5:9: Name 'LINE_TOP_Y' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]mp\shapes\Gorge.java:6:9: Name 'LINE_HEIGHT' must match pattern '^[a-z][a-zA-Z0-9]*$'. [MemberName]
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,67.11409395973155% complete,6.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



104,Wed Jul 09 05:31:19 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.52901023890784% complete,6.6,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



104,Wed Jul 09 05:31:23 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,98.07692307692307% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.52901023890784% complete,6.6,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



104,Wed Jul 09 05:31:28 EDT 2025*PREVIOUS_OUTPUT*


104,Wed Jul 09 05:32:19 EDT 2025*PREVIOUS_OUTPUT*


104,Wed Jul 09 05:32:22 EDT 2025*PREVIOUS_OUTPUT*


105,Wed Jul 09 05:33:36 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.52901023890784% complete,6.6,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



105,Wed Jul 09 05:33:40 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.52901023890784% complete,6.6,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,86.45161290322581% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



106,Wed Jul 09 05:35:58 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,mp\shapes\moveable.java:3:18: Name 'moveable' must match pattern '^[A-Z][a-zA-Z0-9]*$'. [TypeName]
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.88294314381271% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,87.09677419354838% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



106,Wed Jul 09 05:36:01 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,mp\shapes\moveable.java:3:18: Name 'moveable' must match pattern '^[A-Z][a-zA-Z0-9]*$'. [TypeName]
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.88294314381271% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,87.09677419354838% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



107,Wed Jul 09 05:36:51 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.88294314381271% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,87.09677419354838% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



108,Wed Jul 09 05:40:36 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,38.46153846153847% complete,1.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.88294314381271% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,88.38709677419355% complete,6.2,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



109,Wed Jul 09 05:46:29 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.88294314381271% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,88.38709677419355% complete,6.2,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



109,Wed Jul 09 05:46:30 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.88294314381271% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,88.38709677419355% complete,6.2,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



110,Wed Jul 09 05:50:23 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,53.84615384615385% complete,2.7,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.79804560260585% complete,6.6,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



110,Wed Jul 09 05:50:32 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,53.84615384615385% complete,2.7,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.79804560260585% complete,6.6,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



111,Wed Jul 09 05:54:10 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.11627906976744% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



111,Wed Jul 09 05:54:12 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,86.95652173913044% complete,8.7,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.11627906976744% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



112,Wed Jul 09 05:55:41 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.95176848874597% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



113,Wed Jul 09 05:59:17 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.49511400651465% complete,6.4,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



114,Wed Jul 09 06:01:20 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.34653465346535% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



115,Wed Jul 09 06:03:36 EDT 2025*PREVIOUS_OUTPUT*


116,Wed Jul 09 06:10:21 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.11627906976744% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



116,Wed Jul 09 06:10:25 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.11627906976744% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



117,Wed Jul 09 06:13:10 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.11627906976744% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



117,Wed Jul 09 06:13:10 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.11627906976744% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



117,Wed Jul 09 06:13:16 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,46.15384615384615% complete,2.3,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,65.11627906976744% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



118,Wed Jul 09 06:28:12 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.72491909385113% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



119,Wed Jul 09 15:33:39 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



119,Wed Jul 09 15:33:43 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.72491909385113% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



119,Wed Jul 09 15:33:50 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.72491909385113% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



119,Wed Jul 09 15:33:51 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.72491909385113% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.shapes.APolarPoint with tag Locatable has multiple interfaces [interface mp.shapes.Point, interface mp.shapes.Locatable, interface mp.shapes.PolarPointInterface]
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



119,Wed Jul 09 15:33:54 EDT 2025*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.72491909385113% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.shapes.APolarPoint with tag Locatable has multiple interfaces [interface mp.shapes.Point, interface mp.shapes.Locatable, interface mp.shapes.PolarPointInterface]
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
LocatableXProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableXEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape has multiple interfaces [interface mp.shapes.AScalableRectangleInterface, interface mp.shapes.Locatable]
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test LocatableXProperty failed.
Please correct the problems identified by preceding test:LocatableXProperty before running this test
<<
*END_OUTPUT*



119,Wed Jul 09 15:34:00 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,64.72491909385113% complete,6.5,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.shapes.APolarPoint with tag Locatable has multiple interfaces [interface mp.shapes.Point, interface mp.shapes.Locatable, interface mp.shapes.PolarPointInterface]
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
LocatableXProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableXEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScalableRectangle with tag BoundedShape has multiple interfaces [interface mp.shapes.AScalableRectangleInterface, interface mp.shapes.Locatable]
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test LocatableXProperty failed.
Please correct the problems identified by preceding test:LocatableXProperty before running this test
<<
*END_OUTPUT*



120,Wed Jul 09 15:41:20 EDT 2025*PRE_OUTPUT*
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



121,Wed Jul 09 15:52:16 EDT 2025*PRE_OUTPUT*
>>Running test BridgeSceneScrollMethodDefined
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneScrollMethodDefined,100.0% complete,2.0,2.0,
<<
*END_OUTPUT*



121,Wed Jul 09 15:53:20 EDT 2025*PRE_OUTPUT*
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneScrollMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



122,Wed Jul 09 15:58:27 EDT 2025*PRE_OUTPUT*
>>Running test TaggedFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
*END_OUTPUT*



122,Wed Jul 09 15:58:37 EDT 2025*PRE_OUTPUT*
>>Running test LegsFactoryMethodDefined
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
*END_OUTPUT*



122,Wed Jul 09 15:58:39 EDT 2025*PRE_OUTPUT*
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



122,Wed Jul 09 15:58:41 EDT 2025*PRE_OUTPUT*
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



122,Wed Jul 09 15:58:48 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



122,Wed Jul 09 15:58:48 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
*END_OUTPUT*



122,Wed Jul 09 15:59:45 EDT 2025*PREVIOUS_OUTPUT*


123,Wed Jul 09 18:17:01 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.23809523809523% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,63.09148264984227% complete,6.3,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*



123,Wed Jul 09 18:17:01 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
>>Running test BridgeSceneScrollMethodDefined
<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
>>Running test BridgeSceneGalahadScrollLeftArmTestCase
<<
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
>>Running test BridgeSceneArthurScrollRightLegTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.23809523809523% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,63.09148264984227% complete,6.3,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:17: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Method matching scroll not found in class: class main.BridgeSceneImpl
<<
*END_OUTPUT*



124,Wed Jul 09 18:19:43 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,63.55140186915887% complete,6.4,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,99.42528735632183% complete,7.0,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



124,Wed Jul 09 18:19:43 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,63.55140186915887% complete,6.4,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,99.42528735632183% complete,7.0,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



124,Wed Jul 09 18:19:48 EDT 2025*PREVIOUS_OUTPUT*


124,Wed Jul 09 18:19:50 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,95.08196721311475% complete,4.8,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,92.3076923076923% complete,4.6,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,63.55140186915887% complete,6.4,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,99.42528735632183% complete,7.0,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*



125,Wed Jul 09 18:33:24 EDT 2025*PRE_OUTPUT*
>>Running test TaggedFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
*END_OUTPUT*



125,Wed Jul 09 18:33:31 EDT 2025*PRE_OUTPUT*
>>Running test BridgeSceneFactoryMethodDefined
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
*END_OUTPUT*



125,Wed Jul 09 18:33:33 EDT 2025*PRE_OUTPUT*
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
*END_OUTPUT*



125,Wed Jul 09 18:33:35 EDT 2025*PRE_OUTPUT*
>>Running test ConsoleSceneViewSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,0.0% complete,0.0,2.0,No class in project matching name/tag:FactoryClass
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,
Preceding test TaggedFactory failed.
Please correct the problems identified by preceding test:TaggedFactory before running this test
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory class:[factoryclass] not found.
<<
*END_OUTPUT*



126,Wed Jul 09 18:35:16 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



126,Wed Jul 09 18:35:25 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,Factory method:[consolesceneviewfactorymethod] not found.
<<
>>Test Result:
LegsFactoryMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
>>Test Result:
AvatarCallsLegFactoryMethod,0.0% complete,0.0,2.0,
Preceding test BridgeSceneFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneFactoryMethodDefined before running this test
<<
*END_OUTPUT*



126,Wed Jul 09 18:35:29 EDT 2025*PREVIOUS_OUTPUT*


127,Wed Jul 09 18:45:17 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneSemantics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test BridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
BridgeSceneSayMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
BridgeScenePassedMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
BridgeSceneFailedMethodDefined,0.0% complete,0.0,2.0,Internal error, please contact instructor
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,
Preceding test BridgeSceneApproachMethodDefined failed.
Please correct the problems identified by preceding test:BridgeSceneApproachMethodDefined before running this test
<<
*END_OUTPUT*



128,Wed Jul 09 19:06:51 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
*END_OUTPUT*



128,Wed Jul 09 19:06:58 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



128,Wed Jul 09 19:07:01 EDT 2025*PRE_OUTPUT*
>>Running suite A2Observables
<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
>>Running test LocatablePropertyChangeListenersProperty
<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
*END_OUTPUT*



128,Wed Jul 09 19:07:04 EDT 2025*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
>>Running test BridgeSceneScrollMethodDefined
<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
>>Running test BridgeSceneGalahadScrollLeftArmTestCase
<<
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
>>Running test BridgeSceneArthurScrollRightLegTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:17: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
*END_OUTPUT*



128,Wed Jul 09 19:07:06 EDT 2025*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:17: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



128,Wed Jul 09 19:07:34 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:17: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,0.0% complete,0.0,50.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NamedConstants,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NoStarImports,0.0% complete,0.0,2.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2MnemonicNames,0.0% complete,0.0,10.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2Encapsulation,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Test Result:
A2CommonSignaturesAreInherited,0.0% complete,0.0,7.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
*END_OUTPUT*



129,Wed Jul 09 20:16:18 EDT 2025*PRE_OUTPUT*
>>Running suite A2Factory
<<
>>Running test TaggedFactory
<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running test BridgeSceneSingletonFromFactory
<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>Running test LegsFactoryMethodDefined
<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
>>Running test AvatarCallsLegFactoryMethod
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
*END_OUTPUT*



129,Wed Jul 09 20:16:18 EDT 2025*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
*END_OUTPUT*



129,Wed Jul 09 20:16:25 EDT 2025*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*


