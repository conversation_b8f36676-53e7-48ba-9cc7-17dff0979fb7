*PRE_OUTPUT*
>>Running suite CoordinatedAnimation
<<
>>Running test BroadcastingClearanceManagerFactoryMethodDefined
<<
>>Running test BroadcastingClearanceManagerSingletonFromFactory
<<
>>Running test WaitingAvatars
<<
>>Running test AbstractionWaitingAvatars: 
Start the waiting animations of 4 knights, execute proceed all, and see if we subsequently get events from all four anumations
<<
>>Running test LockstepAvatars
<<
>>Running test AbstractionLockstepAvatars: 
Start the animation of af <PERSON>, <PERSON><PERSON> and guard in that order. 
 After the guard is started, wait for property change events from all three avatars to be spaced appropriately in time to show that <PERSON> and <PERSON><PERSON> are moving to the beat set by the guard
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractLocatable,0.0% complete,0.0,3.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
AbstractBoundedShape,0.0% complete,0.0,3.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
TaggedImpossibleAngle,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
CheckedImpossibleAngle,0.0% complete,0.0,5.0,
Preceding test TaggedImpossibleAngle failed.
Please correct the problems identified by preceding test:TaggedImpossibleAngle before running this test
<<
>>Test Result:
TaggedLegs,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedRestrictedLine,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
ArthurIsNotAContortionist,0.0% complete,0.0,30.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
AsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractionAsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncGalahadAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncLancelotAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncRobinAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AssertingBridgeSceneDynamics,0.0% complete,0.0,50.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
BroadcastingClearanceManagerFactoryMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BroadcastingClearanceManagerSingletonFromFactory,0.0% complete,0.0,2.0,
Preceding test BroadcastingClearanceManagerFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerFactoryMethodDefined before running this test
<<
>>Test Result:
WaitingAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
>>Test Result:
AbstractionWaitingAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
>>Test Result:
LockstepAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
>>Test Result:
AbstractionLockstepAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
*END_OUTPUT*
