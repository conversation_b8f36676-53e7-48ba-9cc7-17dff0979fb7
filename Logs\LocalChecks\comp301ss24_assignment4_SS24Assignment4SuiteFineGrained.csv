#,Time,%Passes,Change,Test,Pass,Partial,Fail,Untested,SessionNumber,SessionRunNumber,IsSuite,SuiteTests,PrerequisiteTests,ExtraCreditTests,TestScores,FailFromPreReq,
0,Wed Jul 09 20:04:16 EDT 2025,0,0,A4Style, , ,A4CommonPropertiesAreInherited+ A4CommonSignaturesAreInherited+ A4Encapsulation+ A4InterfaceAsType+ A4MnemonicNames+ A4NamedConstants+ A4NamingConventions+ A4NoHiddenFields+ A4NoStarImports+ A4NonPublicAccessModifiersMatched+ A4PackageDeclarations+ A4PublicMethodsOverride+ A4SimplifyBooleanExpressions+ A4SimplifyBooleanReturns+ AssertingBridgeSceneDynamics+ ,AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars ArthurIsNotAContortionist AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory CheckedImpossibleAngle LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine WaitingAvatars ,0,0,true,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns ,AssertingBridgeSceneDynamics ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4NonPublicAccessModifiersMatched ,A4CommonPropertiesAreInherited-(0.0/2.0) A4CommonSignaturesAreInherited-(0.0/2.0) A4Encapsulation-(0.0/3.0) A4InterfaceAsType-(0.0/7.0) A4MnemonicNames-(0.0/5.0) A4NamedConstants-(0.0/3.0) A4NamingConventions-(0.0/2.0) A4NoHiddenFields-(0.0/1.0) A4NoStarImports-(0.0/1.0) A4NonPublicAccessModifiersMatched-(0.0/5.0) A4PackageDeclarations-(0.0/2.0) A4PublicMethodsOverride-(0.0/5.0) A4SimplifyBooleanExpressions-(0.0/1.0) A4SimplifyBooleanReturns-(0.0/1.0) , ,
1,Wed Jul 09 20:13:42 EDT 2025,0,0,AbstractClasses, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape+ AbstractLocatable+ AssertingBridgeSceneDynamics TaggedBoundedShape+ TaggedLocatable+ ,AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars ArthurIsNotAContortionist AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory CheckedImpossibleAngle LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation TaggedImpossibleAngle TaggedLegs TaggedRestrictedLine WaitingAvatars ,1,0,true,AbstractBoundedShape AbstractLocatable TaggedBoundedShape TaggedLocatable , , ,AbstractBoundedShape-(0.0/3.0) AbstractLocatable-(0.0/3.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
2,Wed Jul 09 20:13:44 EDT 2025,0,0,Exceptions, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable ArthurIsNotAContortionist+ AssertingBridgeSceneDynamics BridgeSceneSingletonFromFactory+ CheckedImpossibleAngle+ TaggedBoundedShape TaggedImpossibleAngle+ TaggedLegs+ TaggedLocatable TaggedRestrictedLine+ ,AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation WaitingAvatars ,1,1,true,ArthurIsNotAContortionist CheckedImpossibleAngle TaggedImpossibleAngle TaggedLegs TaggedRestrictedLine ,BridgeSceneSingletonFromFactory , ,ArthurIsNotAContortionist-(0.0/30.0) CheckedImpossibleAngle-(0.0/5.0) TaggedImpossibleAngle-(0.0/0.0) TaggedLegs-(0.0/0.0) TaggedRestrictedLine-(0.0/0.0) , ,
3,Wed Jul 09 20:13:49 EDT 2025,0,0,AsyncAnimation, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation+ ArthurIsNotAContortionist AssertingBridgeSceneDynamics AsyncArthurAnimation+ AsyncGalahadAnimation+ AsyncLancelotAnimation+ AsyncRobinAnimation+ BridgeSceneSingletonFromFactory CheckedImpossibleAngle TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine ,AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation WaitingAvatars ,1,2,true,AbstractionAsyncArthurAnimation AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation , , ,AbstractionAsyncArthurAnimation-(0.0/10.0) AsyncArthurAnimation-(0.0/10.0) AsyncGalahadAnimation-(0.0/10.0) AsyncLancelotAnimation-(0.0/10.0) AsyncRobinAnimation-(0.0/10.0) , ,
4,Wed Jul 09 20:13:52 EDT 2025,0,0,AssertingBridgeSceneDynamics, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation ArthurIsNotAContortionist AssertingBridgeSceneDynamics AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory CheckedImpossibleAngle TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine ,AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation WaitingAvatars ,1,3,false, , , ,AssertingBridgeSceneDynamics-(0.0/50.0) , ,
5,Wed Jul 09 20:14:02 EDT 2025,0,0,CoordinatedAnimation, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation AbstractionLockstepAvatars+ AbstractionWaitingAvatars+ ArthurIsNotAContortionist AssertingBridgeSceneDynamics AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined+ BroadcastingClearanceManagerSingletonFromFactory+ CheckedImpossibleAngle LockstepAvatars+ TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine WaitingAvatars+ ,AbstractionSyncArthurAnimation SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation ,1,4,true,AbstractionLockstepAvatars AbstractionWaitingAvatars BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory LockstepAvatars WaitingAvatars , , ,AbstractionLockstepAvatars-(0.0/30.0) AbstractionWaitingAvatars-(0.0/30.0) BroadcastingClearanceManagerFactoryMethodDefined-(0.0/2.0) BroadcastingClearanceManagerSingletonFromFactory-(0.0/2.0) LockstepAvatars-(0.0/30.0) WaitingAvatars-(0.0/30.0) , ,
