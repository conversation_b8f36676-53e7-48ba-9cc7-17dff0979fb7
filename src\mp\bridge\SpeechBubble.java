package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape, Locatable {
    private String text = "Grail";
    private int xCoordinate, yCoordinate;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public SpeechBubble() {
    }
    @Override
    public String getText() {
        return text;
    }

    @Override
    public void setText(final String newText) {
        final String oldValue = this.text;
        this.text = newText;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "text", oldValue, newText);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return xCoordinate;
    }

    @Override
    public void setX(final int x) {
        final int oldValue = this.xCoordinate;
        this.xCoordinate = x;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return yCoordinate;
    }

    @Override
    public void setY(final int y) {
        final int oldValue = this.yCoordinate;
        this.yCoordinate = y;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
