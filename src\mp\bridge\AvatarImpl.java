package mp.bridge;

import java.beans.PropertyChangeListener;
import java.util.List;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Moveable;
import util.models.PropertyListenerRegisterer;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable, PropertyListenerRegisterer {
    private static final int AVATAR_VIEWPORT_WIDTH = 200;
    private static final int AVATAR_VIEWPORT_HEIGHT = 300;
    private static final int AVATAR_UNIT_INCREMENT = 5;
    private static final int AVATAR_BLOCK_INCREMENT = 25;

    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }

    private void layoutAtOrigin() {}

    @Override
    public ImageShape getHead() { return head; }

    @Override
    public StringShape getStringShape() { return speech; }

    @Override
    public Angle getArms() { return arms; }

    @Override
    public Angle getLegs() { return legs; }

    @Override
    public void move(final int deltaX, final int deltaY) {
        head.setX(head.getX() + deltaX);
        head.setY(head.getY() + deltaY);
        arms.move(deltaX, deltaY);
        legs.move(deltaX, deltaY);
        speech.setX(speech.getX() + deltaX);
        speech.setY(speech.getY() + deltaY);
        layoutAtOrigin();
    }

    @Override
    public void scroll(final String limb, final int deltaX, final int deltaY) {
        if ("left arm".equalsIgnoreCase(limb)) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + deltaX);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + deltaY);
        } else if ("right arm".equalsIgnoreCase(limb)) {
            arms.getRightLine().setX(arms.getRightLine().getX() + deltaX);
            arms.getRightLine().setY(arms.getRightLine().getY() + deltaY);
        } else if ("left leg".equalsIgnoreCase(limb)) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + deltaX);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + deltaY);
        } else if ("right leg".equalsIgnoreCase(limb)) {
            legs.getRightLine().setX(legs.getRightLine().getX() + deltaX);
            legs.getRightLine().setY(legs.getRightLine().getY() + deltaY);
        }
    }

    @Override
    public void scale(final double factor) {
        head.setWidth((int) (head.getWidth() * factor));
        head.setHeight((int) (head.getHeight() * factor));
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        head.addPropertyChangeListener(listener);
        speech.addPropertyChangeListener(listener);
        // if arms/legs support property listening:
        arms.getLeftLine().addPropertyChangeListener(listener);
        arms.getRightLine().addPropertyChangeListener(listener);
        legs.getLeftLine().addPropertyChangeListener(listener);
        legs.getRightLine().addPropertyChangeListener(listener);
    }

    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return head.getPropertyChangeListeners(); // or merged list if needed
    }

    // Scrollable interface methods
    @Override
    public java.awt.Dimension getPreferredScrollableViewportSize() {
        return new java.awt.Dimension(AVATAR_VIEWPORT_WIDTH, AVATAR_VIEWPORT_HEIGHT);
    }

    @Override
    public int getScrollableUnitIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return AVATAR_UNIT_INCREMENT;
    }

    @Override
    public int getScrollableBlockIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return AVATAR_BLOCK_INCREMENT;
    }

    @Override
    public boolean getScrollableTracksViewportWidth() {
        return false;
    }

    @Override
    public boolean getScrollableTracksViewportHeight() {
        return false;
    }
}
