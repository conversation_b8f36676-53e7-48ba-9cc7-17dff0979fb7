package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.ROTATING_LINE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int xOffset, yOffset;
    private static final double UNIT = Math.PI / 32;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public RotatingLine() {
        this.xOffset = 0;
        this.yOffset = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return xOffset + point.getX();
    }

    @Override
    public void setX(final int x) {
        final int oldValue = this.xOffset;
        this.xOffset = x;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return yOffset + point.getY();
    }

    @Override
    public void setY(final int y) {
        final int oldValue = this.yOffset;
        this.yOffset = y;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
    @Override
    public int getWidth() {
        return point.getX();
    }
    @Override
    public int getHeight() {
        return point.getY();
    }

    @Override
    public void setWidth(final int width) {
        // For a rotating line, width doesn't have a direct meaning
        // but we need to implement it for BoundedShape interface
    }

    @Override
    public void setHeight(final int height) {
        // For a rotating line, height doesn't have a direct meaning
        // but we need to implement it for BoundedShape interface
    }
    public double getRadius() {
        return point.getRadius();
    }

    @Override
    public void setRadius(final double radius) {
        point = new APolarPoint(radius, point.getAngle());
    }

    public double getAngle() {
        return point.getAngle();
    }

    @Override
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    @Override
    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override
    public void move(final int deltaX, final int deltaY) {
        setX(xOffset + deltaX);
        setY(yOffset + deltaY);
    }

    // Additional getters/setters for ROTATING_LINE requirements
    public int getXProperty() {
        return getX();
    }

    public void setXProperty(final int x) {
        setX(x);
    }

    public int getYProperty() {
        return getY();
    }

    public void setYProperty(final int y) {
        setY(y);
    }

    public double getRadiusProperty() {
        return getRadius();
    }

    public void setRadiusProperty(final double radius) {
        setRadius(radius);
    }

    public double getAngleProperty() {
        return getAngle();
    }

    public void setAngleProperty(final double angle) {
        setAngle(angle);
    }

    // PropertyListenerRegisterer methods
    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
