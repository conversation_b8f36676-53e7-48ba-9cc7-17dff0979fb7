Starting audit...
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component angle in Identifier SOME_ANGLE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component args in Identifier args is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component count in Identifier COUNT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component delta in Identifier DELTA is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component frame in Identifier frame is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component i in Identifier i is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component line in Identifier line is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component ms in Identifier SLEEP_MS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component radius in Identifier SOME_RADIUS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component scene in Identifier scene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component sleep in Identifier SLEEP_MS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component some in Identifier SOME_ANGLE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component some in Identifier SOME_RADIUS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component start in Identifier START_X is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component start in Identifier START_Y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component x in Identifier START_X is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component y in Identifier START_Y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Expected type names/tags: [main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has made expected call (.*)!sleep:long->void. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has made expected call @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE. Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has not made expected call @Comp301Tags.FACTORY_CLASS!@consoleSceneViewFactoryMethod:->.*. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Access modifiers used: Access Modifiers Used: [(main.Assignment2, public, private, 3, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:7 Public Variables Fraction:1.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:0.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:1.1428571428571428 Average Local References per Variable:1.1428571428571428 Average Local Assignments per Variable:1.1428571428571428 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Component assignment in Identifier main.Assignment2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Component main in Identifier main.Assignment2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[static public  animateLine:->void, static public  main:String[]->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Type main.Assignment2 matches tags (main.Assignment2)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:10:29: Named Constant SOME_RADIUS defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:11:32: Named Constant SOME_ANGLE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:12:29: Named Constant START_X defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:13:29: Named Constant START_Y defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:14:29: Named Constant DELTA defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:15:29: Named Constant COUNT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:16:30: Named Constant SLEEP_MS defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:19: Interface RotateLine used as the type of variable/function line. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:32:29: Final parameter args defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:33: Interface BridgeScene used as the type of variable/function scene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_HEIGHT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_WIDTH is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier guardArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier knightArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component arthur in Identifier arthur is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component avatar in Identifier avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component avatar in Identifier currentAvatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier GALAHAD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier GUARD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier LANCELOT_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier ROBIN_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component coordinate in Identifier AREA_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component coordinate in Identifier GORGE_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component coordinate in Identifier GUARD_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component coordinate in Identifier KNIGHT_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component coordinate in Identifier SOME_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component coordinate in Identifier SOME_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component coordinate in Identifier gorgeYCoordinate is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component current in Identifier currentAvatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component difference in Identifier difference is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component direction in Identifier direction is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component galahad in Identifier GALAHAD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component galahad in Identifier galahad is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gorge in Identifier GORGE_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gorge in Identifier gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gorge in Identifier gorgeYCoordinate is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier GUARD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier GUARD_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier guard is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier guardArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component height in Identifier AREA_HEIGHT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier KNIGHT_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier knightArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier knightTurn is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component lancelot in Identifier LANCELOT_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component lancelot in Identifier lancelot is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component limb in Identifier limb is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component occupied in Identifier occupied is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component orientation in Identifier orientation is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component rect in Identifier visibleRect is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component robin in Identifier ROBIN_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component robin in Identifier robin is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component some in Identifier SOME_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component some in Identifier SOME_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component speech in Identifier speechText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component text in Identifier speechText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component turn in Identifier knightTurn is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component visible in Identifier visibleRect is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component width in Identifier AREA_WIDTH is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component x in Identifier AREA_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component x in Identifier GORGE_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component x in Identifier SOME_X_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component y in Identifier GUARD_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component y in Identifier KNIGHT_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component y in Identifier SOME_Y_COORDINATE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component y in Identifier gorgeYCoordinate is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Arthur of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Galahad of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Guard of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Lancelot of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Robin of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected instantiation of @Comp301Tags.AVATAR in type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE] by methods [public  BridgeSceneImpl:->]. Good! [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Property readonly  p-v:5 access:public PreferredScrollableViewportSize:java.awt.Dimension(public , null) common between BridgeSceneImpl and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Property readonly  p-v:5 access:public ScrollableTracksViewportHeight:boolean(public , null) common between BridgeSceneImpl and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Property readonly  p-v:5 access:public ScrollableTracksViewportWidth:boolean(public , null) common between BridgeSceneImpl and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: The following public methods do not override: [public  getArthur:->mp.bridge.Avatar, public  getLancelot:->mp.bridge.Avatar, public  getRobin:->mp.bridge.Avatar, public  getGalahad:->mp.bridge.Avatar, public  getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Access modifiers used: Access Modifiers Used: [(main.BridgeSceneImpl, public, private, 3, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:20 Number of Functions:15 Number of Non Getter Functions:2 Number of Getters and Setters:13 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:25 Public Variables Fraction:0.24 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.04 Private  Variable Fraction:0.72 Average Variable Access:2.24 Number of Properties:13 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.7058823529411766 Average Local References per Variable:3.2 Average Local Assignments per Variable:3.2 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Component bridge in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Component impl in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Component main in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Component scene in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Expected signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Expected signature failed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Expected signature passed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Expected signature say:String->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Interfaces defined: [mp.bridge.BridgeScene]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Methods defined: NonGetterFunctions:[public  getScrollableUnitIncrement:java;int;int->int, public  getScrollableBlockIncrement:java;int;int->int]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void, public  scroll:String;int;int->void]Getters:[public  getArthur:->mp.bridge.Avatar, public  getLancelot:->mp.bridge.Avatar, public  getRobin:->mp.bridge.Avatar, public  getGalahad:->mp.bridge.Avatar, public  getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean, public  getPreferredScrollableViewportSize:->java.awt.Dimension, public  getScrollableTracksViewportWidth:->boolean, public  getScrollableTracksViewportHeight:->boolean]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Properties defined: Properties:[readonly  p-v:3 access:public KnightTurn:boolean(public , null), readonly  p-v:3 access:public Occupied:boolean(public , null), readonly  p-v:3 access:public Arthur:mp.bridge.Avatar(public , null), readonly  p-v:5 access:public ScrollableTracksViewportWidth:boolean(public , null), readonly  p-v:3 access:public Gorge:mp.shapes.Gorge(public , null), readonly  p-v:3 access:public KnightArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:3 access:public Guard:mp.bridge.Avatar(public , null), readonly  p-v:5 access:public ScrollableTracksViewportHeight:boolean(public , null), readonly  p-v:3 access:public Lancelot:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public GuardArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:3 access:public Galahad:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Robin:mp.bridge.Avatar(public , null), readonly  p-v:5 access:public PreferredScrollableViewportSize:java.awt.Dimension(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Type main.BridgeSceneImpl matches tags (@Comp301Tags.BRIDGE_SCENE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17:1: Class Data Abstraction Coupling is 9 (max allowed is 7) classes [AScalableRectangle, ArthurHead, AvatarImpl, GalahadHead, Gorge, GuardHead, LancelotHead, RobinHead, java.awt.Dimension]. [ClassDataAbstractionCoupling]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:20: Interface Avatar used as the type of variable/function arthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:20: Interface Avatar used as the type of variable/function galahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:20: Interface Avatar used as the type of variable/function guard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:20: Interface Avatar used as the type of variable/function lancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:20: Interface Avatar used as the type of variable/function robin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:21:29: Named Constant SOME_X_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22:29: Named Constant SOME_Y_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:23:29: Named Constant LANCELOT_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:24:29: Named Constant ROBIN_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:25:29: Named Constant GALAHAD_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:26:29: Named Constant GUARD_CONSTANT defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:27: Class Gorge rather than interface used as the type of variable/function gorge. [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:28: Interface Avatar used as the type of variable/function currentAvatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:29: Interface AScalableRectangleInterface used as the type of variable/function knightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:30: Interface AScalableRectangleInterface used as the type of variable/function guardArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:32:30: Named Constant AREA_X_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:33:30: Named Constant KNIGHT_Y_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:34:30: Named Constant GUARD_Y_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:35:30: Named Constant AREA_WIDTH defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:36:30: Named Constant AREA_HEIGHT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:38:30: Named Constant GORGE_X_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:58: Interface Avatar used as the type of variable/function getArthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:62: Interface Avatar used as the type of variable/function getLancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:66: Interface Avatar used as the type of variable/function getRobin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:70: Interface Avatar used as the type of variable/function getGalahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:74: Interface Avatar used as the type of variable/function getGuard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:79: Interface AScalableRectangleInterface used as the type of variable/function getKnightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:83: Interface AScalableRectangleInterface used as the type of variable/function getGuardArea. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:87: Class Gorge rather than interface used as the type of variable/function getGorge. [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:124: Interface Avatar used as the type of variable/function avatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:124:26: Final parameter avatar defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:130:21: Final parameter speechText defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:144: Signatures public  scroll:String;int;int->void common with mp.bridge.AvatarImpl not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:145:24: Final parameter limb defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:145:43: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:145:61: Final parameter deltaY defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:150: Signatures public  getPreferredScrollableViewportSize:->java.awt.Dimension common with mp.bridge.AvatarImpl not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:152: Magic number 600   [LiberalMagicNumber]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:152: Magic number 800   [LiberalMagicNumber]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:155: Signatures public  getScrollableUnitIncrement:java;int;int->int common with mp.bridge.AvatarImpl not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:156:43: Final parameter visibleRect defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:156:81: Final parameter orientation defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:156:104: Final parameter direction defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:157: Magic number 10   [LiberalMagicNumber]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:160: Signatures public  getScrollableBlockIncrement:java;int;int->int common with mp.bridge.AvatarImpl not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:161:44: Final parameter visibleRect defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:161:82: Final parameter orientation defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:161:105: Final parameter direction defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:162: Magic number 50   [LiberalMagicNumber]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:165: Signatures public  getScrollableTracksViewportWidth:->boolean common with mp.bridge.AvatarImpl not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:170: Signatures public  getScrollableTracksViewportHeight:->boolean common with mp.bridge.AvatarImpl not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Component console in Identifier main.ConsoleSceneView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Component main in Identifier main.ConsoleSceneView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Component scene in Identifier main.ConsoleSceneView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Component view in Identifier main.ConsoleSceneView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Super types: [java.beans.PropertyChangeListener] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Type main.ConsoleSceneView matches tags (@Comp301Tags.CONSOLE_SCENE_VIEW)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneView.java:7: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:0: Component evt in Identifier evt is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:0: Component instance in Identifier instance is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:0: Function getInstance assigns to a global variable  [FunctionAssignsGlobal]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:1: Some method (getInstance:->ConsoleSceneView) in class main.ConsoleSceneViewImpl:[@Comp301Tags.CONSOLE_SCENE_VIEW] has made expected call (.*)!addPropertyChangeListener:PropertyChangeListener->void. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:1: Some method (getInstance:->ConsoleSceneView) in class main.ConsoleSceneViewImpl:[@Comp301Tags.CONSOLE_SCENE_VIEW] has made expected call @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:1: Some method (propertyChange:java.beans.PropertyChangeEvent->void) in class main.ConsoleSceneViewImpl:[@Comp301Tags.CONSOLE_SCENE_VIEW] has made expected call java.io.PrintStream!println:*->.*. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Access modifiers used: Access Modifiers Used: [(main.ConsoleSceneViewImpl, public, package, 2, main.StaticFactoryClass, null ), (main.ConsoleSceneViewImpl, public, private, 3, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:1 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:1 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:1 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Component console in Identifier main.ConsoleSceneViewImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Component impl in Identifier main.ConsoleSceneViewImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Component main in Identifier main.ConsoleSceneViewImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Component scene in Identifier main.ConsoleSceneViewImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Component view in Identifier main.ConsoleSceneViewImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Expected interface java.beans.PropertyChangeListener of class main.ConsoleSceneViewImpl[@Comp301Tags.CONSOLE_SCENE_VIEW]. Good! [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Interfaces defined: [main.ConsoleSceneView]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  propertyChange:java.beans.PropertyChangeEvent->void]Getters:[static public  getInstance:->main.ConsoleSceneView]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Properties defined: Properties:[readonly  p-v:3 access:public Instance:main.ConsoleSceneView(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Type main.ConsoleSceneViewImpl matches tags (@Comp301Tags.CONSOLE_SCENE_VIEW)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:13: Interface ConsoleSceneView used as the type of variable/function instance. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:16: Interface BridgeScene used as the type of variable/function scene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:17: Interface Avatar used as the type of variable/function knight. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:32: Interface ConsoleSceneView used as the type of variable/function getInstance. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:40:32: Final parameter evt defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component args in Identifier args is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component max in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component max in Identifier MAX_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component printed in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component process in Identifier PROCESS_TIMEOUT_S is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component s in Identifier PROCESS_TIMEOUT_S does  not have at least 2 letters [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component s in Identifier PROCESS_TIMEOUT_S does not have at least 1 vowels [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component s in Identifier PROCESS_TIMEOUT_S is not in dictionary [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component timeout in Identifier PROCESS_TIMEOUT_S is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component traces in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component traces in Identifier MAX_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Access modifiers used: Access Modifiers Used: [(main.RunSS25A2Tests, public, private, 3, main.RunSS25A2Tests, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:1.0 Average Local References per Variable:1.0 Average Local Assignments per Variable:1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component a in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component main in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component run in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component tests in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[static public  main:String[]->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:7:35: Named Constant MAX_PRINTED_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:8:43: Named Constant MAX_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:9:43: Named Constant PROCESS_TIMEOUT_S defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:10:41: Parameter args should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component scene in Identifier scene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Access modifiers used: Access Modifiers Used: [(main.StaticFactoryClass, public, private, 3, main.StaticFactoryClass, null ), (main.StaticFactoryClass, public, package, 2, main.Assignment2, null ), (main.StaticFactoryClass, public, package, 2, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:7 Number of Functions:7 Number of Non Getter Functions:7 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:1 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:NaN Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component class in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component factory in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component main in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component static in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Methods defined: NonGetterFunctions:[static public  bridgeSceneFactoryMethod:->mp.bridge.BridgeScene, static public  consoleSceneViewFactoryMethod:->main.ConsoleSceneView, static public  legsFactoryMethod:->mp.bridge.Angle, static public  inheritingBridgeScenePainterFactoryMethod:->Object, static public  observableBridgeScenePainterFactoryMethod:->Object, static public  delegatingBridgeSceneViewFactoryMethod:->Object, static public  bridgeSceneControllerFactoryMethod:->Object]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @bridgeSceneControllerFactoryMethod:->@Comp301Tags.BRIDGE_SCENE_CONTROLLER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @delegatingBridgeSceneViewFactoryMethod:->@Comp301Tags.DELEGATING_BRIDGE_SCENE_VIEW in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @inheritingBridgeScenePainterFactoryMethod:->@Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @observableBridgeScenePainterFactoryMethod:->@Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Type main.StaticFactoryClass matches tags (@Comp301Tags.FACTORY_CLASS)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:11: Interface BridgeScene used as the type of variable/function scene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:13: Interface BridgeScene used as the type of variable/function bridgeSceneFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:20: Interface ConsoleSceneView used as the type of variable/function consoleSceneViewFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:24: Interface Angle used as the type of variable/function legsFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.Angle, public, public, 0, main.Assignment2, null ), (mp.bridge.Angle, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.Angle, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:3 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Component angle in Identifier mp.bridge.Angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Component bridge in Identifier mp.bridge.Angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default move:int;int->void]Getters:[default getLeftLine:->mp.shapes.RotateLine, default getRightLine:->mp.shapes.RotateLine]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Properties defined: Properties:[readonly  p-v:5 access:package RightLine:mp.shapes.RotateLine(default , null), readonly  p-v:5 access:package LeftLine:mp.shapes.RotateLine(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Super types: [mp.shapes.Moveable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Type mp.bridge.Angle matches tags (@Comp301Tags.ANGLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:13: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:14: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component file in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component file in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component height in Identifier newHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component name in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component name in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component new in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component new in Identifier newHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component new in Identifier newWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component value in Identifier value is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component width in Identifier newWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Expected getter for property Height of type int in parent type mp.bridge.ArthurHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Expected getter for property Width of type int in parent type mp.bridge.ArthurHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.bridge.ArthurHead[@Comp301Tags.BOUNDED_SHAPE] by methods [public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Expected setter for property Height of type int in parent type mp.bridge.ArthurHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Expected setter for property Width of type int in parent type mp.bridge.ArthurHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between ArthurHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between ArthurHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Some method (setImageFileName:String->void) in class mp.bridge.ArthurHead:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Access modifiers used: Access Modifiers Used: [(mp.bridge.ArthurHead, public, private, 3, mp.bridge.ArthurHead, null ), (mp.bridge.ArthurHead, public, public, 0, main.Assignment2, null ), (mp.bridge.ArthurHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.ArthurHead, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:5 Number of Methods:13 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:11 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:6 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.8333333333333334 ReadOnly Access Properties Fraction:0.16666666666666666 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:9.0 Average Local References per Variable:4.0 Average Local Assignments per Variable:4.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Component arthur in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Component bridge in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Component head in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Missing supertype: @Comp301Tags.LOCATABLE of type mp.bridge.ArthurHead[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Super types: mp.bridge.ImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Type mp.bridge.ArthurHead matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:28:34: Final parameter newFileName defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:37: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:37: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:37: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:37: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:37: Signatures public  getX:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:42: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:42: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:42: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:42: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:42: Signatures public  setX:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:43:22: Final parameter value defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:52: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:52: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:52: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:52: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:52: Signatures public  getY:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:57: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:57: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:57: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:57: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:57: Signatures public  setY:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:58:22: Final parameter value defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:67: Signatures public  getWidth:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:67: Signatures public  getWidth:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:67: Signatures public  getWidth:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:67: Signatures public  getWidth:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:67: Signatures public  getWidth:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:72: Signatures public  setWidth:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:73:26: Final parameter newWidth defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:82: Signatures public  getHeight:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:82: Signatures public  getHeight:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:82: Signatures public  getHeight:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:82: Signatures public  getHeight:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:82: Signatures public  getHeight:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:87: Signatures public  setHeight:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:88:27: Final parameter newHeight defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:103:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:109:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component dx in Identifier dx does not have at least 1 vowels [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component dx in Identifier dx is not in dictionary [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component dy in Identifier dy does not have at least 1 vowels [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component dy in Identifier dy is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component factor in Identifier factor is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component limb in Identifier limb is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Access modifiers used: Access Modifiers Used: [(mp.bridge.Avatar, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.Avatar, public, public, 0, main.Assignment2, null ), (mp.bridge.Avatar, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:7 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:7 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:4 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Component avatar in Identifier mp.bridge.Avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Component bridge in Identifier mp.bridge.Avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default move:int;int->void, default scale:double->void, default scroll:String;int;int->void]Getters:[default getHead:->mp.bridge.ImageShape, default getStringShape:->mp.bridge.StringShape, default getArms:->mp.bridge.Angle, default getLegs:->mp.bridge.Angle]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Properties defined: Properties:[readonly  p-v:5 access:package Legs:mp.bridge.Angle(default , null), readonly  p-v:5 access:package Head:mp.bridge.ImageShape(default , null), readonly  p-v:5 access:package StringShape:mp.bridge.StringShape(default , null), readonly  p-v:5 access:package Arms:mp.bridge.Angle(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Super types: [mp.shapes.Moveable, javax.swing.Scrollable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Type mp.bridge.Avatar matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:14: Class ImageShape rather than interface used as the type of variable/function getHead. [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:15: Interface StringShape used as the type of variable/function getStringShape. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:16: Interface Angle used as the type of variable/function getArms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:17: Interface Angle used as the type of variable/function getLegs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component arms in Identifier arms is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component direction in Identifier direction is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component factor in Identifier factor is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component head in Identifier head is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component legs in Identifier legs is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component limb in Identifier limb is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component orientation in Identifier orientation is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component rect in Identifier visibleRect is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component speech in Identifier speech is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component visible in Identifier visibleRect is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Class mp.bridge.AvatarImpl with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AvatarImpl and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:5 access:public PreferredScrollableViewportSize:java.awt.Dimension(public , null) common between AvatarImpl and main.BridgeSceneImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:5 access:public ScrollableTracksViewportHeight:boolean(public , null) common between AvatarImpl and main.BridgeSceneImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Property readonly  p-v:5 access:public ScrollableTracksViewportWidth:boolean(public , null) common between AvatarImpl and main.BridgeSceneImpl not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Some method (AvatarImpl:mp.bridge.ImageShape->) in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: The following public methods do not override: [public  scale:double->void, public  getPropertyChangeListeners:->java.util.List] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Access modifiers used: Access Modifiers Used: [(mp.bridge.AvatarImpl, public, public, 0, main.Assignment2, null ), (mp.bridge.AvatarImpl, public, private, 3, mp.bridge.AvatarImpl, null ), (mp.bridge.AvatarImpl, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:15 Number of Functions:10 Number of Non Getter Functions:2 Number of Getters and Setters:8 Number of Non Public Methods:1 Public Methods Fraction:0.9333333333333333 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.06666666666666667 Average Method Access:0.2 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:8 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:11.25 Average Local References per Variable:11.25 Average Local Assignments per Variable:11.25 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Component avatar in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Component bridge in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Component impl in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Expected signature move:int;int->void in type mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Expected signature scale:double->void//EC in type mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Interfaces defined: [mp.bridge.Avatar, mp.shapes.Moveable, util.models.PropertyListenerRegisterer]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Methods defined: NonGetterFunctions:[public  getScrollableUnitIncrement:java;int;int->int, public  getScrollableBlockIncrement:java;int;int->int]NonSetterProcedures:[private  layoutAtOrigin:->void, public  move:int;int->void, public  scroll:String;int;int->void, public  scale:double->void, public  addPropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getHead:->mp.bridge.ImageShape, public  getStringShape:->mp.bridge.StringShape, public  getArms:->mp.bridge.Angle, public  getLegs:->mp.bridge.Angle, public  getPropertyChangeListeners:->java.util.List, public  getPreferredScrollableViewportSize:->java.awt.Dimension, public  getScrollableTracksViewportWidth:->boolean, public  getScrollableTracksViewportHeight:->boolean]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Properties defined: Properties:[readonly  p-v:3 access:public Legs:mp.bridge.Angle(public , null), readonly  p-v:3 access:public Head:mp.bridge.ImageShape(public , null), readonly  p-v:3 access:public StringShape:mp.bridge.StringShape(public , null), readonly  p-v:3 access:public Arms:mp.bridge.Angle(public , null), readonly  p-v:5 access:public ScrollableTracksViewportWidth:boolean(public , null), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), readonly  p-v:5 access:public ScrollableTracksViewportHeight:boolean(public , null), readonly  p-v:5 access:public PreferredScrollableViewportSize:java.awt.Dimension(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Type mp.bridge.AvatarImpl matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:16: Class ImageShape rather than interface used as the type of variable/function head. [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:17: Interface StringShape used as the type of variable/function speech. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18: Interface Angle used as the type of variable/function arms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:19: Interface Angle used as the type of variable/function legs. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:21: Class ImageShape rather than interface used as the type of variable/function h. [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:21:23: Final parameter h defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:32: Class ImageShape rather than interface used as the type of variable/function getHead. [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:35: Interface StringShape used as the type of variable/function getStringShape. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:38: Interface Angle used as the type of variable/function getArms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:41: Interface Angle used as the type of variable/function getLegs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:43: Signatures public  move:int;int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:55:24: Final parameter limb defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:55:43: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:55:61: Final parameter deltaY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:56: In method scroll, found else branching. Good! [ElseBranching]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:56:40: Equals invoked on constant "left arm" rather than variable limb. Good! [EqualsAvoidedNull]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:59: In method scroll, found else branching. Good! [ElseBranching]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:59:48: Equals invoked on constant "right arm" rather than variable limb. Good! [EqualsAvoidedNull]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:62: In method scroll, found else branching. Good! [ElseBranching]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:62:47: Equals invoked on constant "left leg" rather than variable limb. Good! [EqualsAvoidedNull]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:65:48: Equals invoked on constant "right leg" rather than variable limb. Good! [EqualsAvoidedNull]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:71:23: Final parameter factor defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.ArthurHead defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.APolarPoint defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:76: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.RotatingLine defined in common types [util.models.PropertyListenerRegisterer, mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:77:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.ArthurHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GalahadHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.LancelotHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.APolarPoint not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.AScalableRectangle not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:87: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.RotatingLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:94: Magic number 200   [LiberalMagicNumber]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:94: Magic number 300   [LiberalMagicNumber]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:98:43: Final parameter visibleRect defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:98:81: Final parameter orientation defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:98:104: Final parameter direction defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:99: Magic number 5   [LiberalMagicNumber]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:103:44: Final parameter visibleRect defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:103:82: Final parameter orientation defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:103:105: Final parameter direction defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:104: Magic number 25   [LiberalMagicNumber]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component avatar in Identifier avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component limb in Identifier limb is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component speech in Identifier speechText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component text in Identifier speechText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.BridgeScene, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:15 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:15 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:10 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Component bridge in Identifier mp.bridge.BridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Component scene in Identifier mp.bridge.BridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default passed:->void, default failed:->void, default approach:mp.bridge.Avatar->void, default say:String->void, default scroll:String;int;int->void]Getters:[default getArthur:->mp.bridge.Avatar, default getLancelot:->mp.bridge.Avatar, default getRobin:->mp.bridge.Avatar, default getGalahad:->mp.bridge.Avatar, default getGuard:->mp.bridge.Avatar, default getKnightArea:->mp.shapes.AScalableRectangleInterface, default getGuardArea:->mp.shapes.AScalableRectangleInterface, default getGorge:->mp.shapes.Gorge, default getOccupied:->boolean, default getKnightTurn:->boolean]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Properties defined: Properties:[readonly  p-v:5 access:package KnightTurn:boolean(default , null), readonly  p-v:5 access:package Gorge:mp.shapes.Gorge(default , null), readonly  p-v:5 access:package Occupied:boolean(default , null), readonly  p-v:5 access:package Arthur:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package KnightArea:mp.shapes.AScalableRectangleInterface(default , null), readonly  p-v:5 access:package Guard:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Lancelot:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package GuardArea:mp.shapes.AScalableRectangleInterface(default , null), readonly  p-v:5 access:package Galahad:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Robin:mp.bridge.Avatar(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Type mp.bridge.BridgeScene matches tags (@Comp301Tags.BRIDGE_SCENE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:13: Interface Avatar used as the type of variable/function getArthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:14: Interface Avatar used as the type of variable/function getLancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:15: Interface Avatar used as the type of variable/function getRobin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:16: Interface Avatar used as the type of variable/function getGalahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:17: Interface Avatar used as the type of variable/function getGuard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:20: Interface Avatar used as the type of variable/function avatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:22: Interface AScalableRectangleInterface used as the type of variable/function getKnightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:23: Interface AScalableRectangleInterface used as the type of variable/function getGuardArea. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:24: Class Gorge rather than interface used as the type of variable/function getGorge. [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component file in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component file in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component name in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component name in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component new in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component new in Identifier newWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component value in Identifier value is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component width in Identifier newWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Expected getter for property Height of type int in parent type mp.bridge.GalahadHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Expected getter for property Width of type int in parent type mp.bridge.GalahadHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.bridge.GalahadHead[@Comp301Tags.BOUNDED_SHAPE] by methods [public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Expected setter for property Height of type int in parent type mp.bridge.GalahadHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Expected setter for property Width of type int in parent type mp.bridge.GalahadHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GalahadHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GalahadHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Some method (setImageFileName:String->void) in class mp.bridge.GalahadHead:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Access modifiers used: Access Modifiers Used: [(mp.bridge.GalahadHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GalahadHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.GalahadHead, public, private, 3, mp.bridge.GalahadHead, null ), (mp.bridge.GalahadHead, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:5 Number of Methods:13 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:11 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:6 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.8333333333333334 ReadOnly Access Properties Fraction:0.16666666666666666 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:9.0 Average Local References per Variable:4.5 Average Local Assignments per Variable:4.5 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Component bridge in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Component galahad in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Component head in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Missing supertype: @Comp301Tags.LOCATABLE of type mp.bridge.GalahadHead[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Super types: mp.bridge.ImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Type mp.bridge.GalahadHead matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:28:34: Final parameter newFileName defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:37: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:37: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:37: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:37: Signatures public  getX:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:42: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:42: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:42: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:42: Signatures public  setX:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:43:22: Final parameter value defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:52: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:52: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:52: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:52: Signatures public  getY:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:57: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:57: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:57: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:57: Signatures public  setY:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:58:22: Final parameter value defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:67: Signatures public  getWidth:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:67: Signatures public  getWidth:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:67: Signatures public  getWidth:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:67: Signatures public  getWidth:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:72: Signatures public  setWidth:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:73:26: Final parameter newWidth defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:82: Signatures public  getHeight:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:82: Signatures public  getHeight:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:82: Signatures public  getHeight:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:82: Signatures public  getHeight:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:87: Signatures public  setHeight:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:88:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:88:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:89:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:91:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:103:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:109:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component file in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component name in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component value in Identifier value is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Expected getter for property Height of type int in parent type mp.bridge.GuardHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Expected getter for property Width of type int in parent type mp.bridge.GuardHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.bridge.GuardHead[@Comp301Tags.BOUNDED_SHAPE] by methods [public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Expected setter for property Height of type int in parent type mp.bridge.GuardHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Expected setter for property Width of type int in parent type mp.bridge.GuardHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GuardHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GuardHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Some method (setImageFileName:String->void) in class mp.bridge.GuardHead:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Access modifiers used: Access Modifiers Used: [(mp.bridge.GuardHead, public, private, 3, mp.bridge.GuardHead, null ), (mp.bridge.GuardHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GuardHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.GuardHead, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:5 Number of Methods:13 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:11 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:6 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.8333333333333334 ReadOnly Access Properties Fraction:0.16666666666666666 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:9.0 Average Local References per Variable:5.5 Average Local Assignments per Variable:5.5 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Component bridge in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Component guard in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Component head in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Missing supertype: @Comp301Tags.LOCATABLE of type mp.bridge.GuardHead[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Super types: mp.bridge.ImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Type mp.bridge.GuardHead matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:28:34: Final parameter fileName defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:28:47: 'fileName' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:31:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:43:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:44:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:46:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:58:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:59:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:61:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:73:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:73:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:74:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:76:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:88:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:88:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:89:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:91:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:103:43: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:109:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:0: Component file in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:0: Component name in Identifier fileName is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:0: Setter setImageFileName does not assign to a global variable [SetterAssignsGlobal]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:1: Missing getter for property Height of type int in parent type mp.bridge.ImageShape[@Comp301Tags.BOUNDED_SHAPE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:1: Missing getter for property Width of type int in parent type mp.bridge.ImageShape[@Comp301Tags.BOUNDED_SHAPE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:1: Missing instantiation of java.beans.PropertyChangeEvent in type mp.bridge.ImageShape[@Comp301Tags.BOUNDED_SHAPE]. [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:1: Missing setter for property Height of type int in parent type mp.bridge.ImageShape[@Comp301Tags.BOUNDED_SHAPE]. [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:1: Missing setter for property Width of type int in parent type mp.bridge.ImageShape[@Comp301Tags.BOUNDED_SHAPE]. [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:1: No method in class mp.bridge.ImageShape:[@Comp301Tags.BOUNDED_SHAPE] has not made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:1: The following public methods do not override: [public  getImageFileName:->String, public  setImageFileName:String->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:2 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Component bridge in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Component image in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Component shape in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Interfaces defined: [mp.shapes.BoundedShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String]Setters:[public  setImageFileName:String->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Missing supertype: @Comp301Tags.LOCATABLE of type mp.bridge.ImageShape[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Properties defined: Properties:[editable, g-s:0 p-v:5 access:public ImageFileName:String(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Type mp.bridge.ImageShape matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component file in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component file in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component name in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component name in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component new in Identifier newFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component value in Identifier value is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Expected getter for property Height of type int in parent type mp.bridge.LancelotHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Expected getter for property Width of type int in parent type mp.bridge.LancelotHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.bridge.LancelotHead[@Comp301Tags.BOUNDED_SHAPE] by methods [public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Expected setter for property Height of type int in parent type mp.bridge.LancelotHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Expected setter for property Width of type int in parent type mp.bridge.LancelotHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between LancelotHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between LancelotHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Some method (setImageFileName:String->void) in class mp.bridge.LancelotHead:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Access modifiers used: Access Modifiers Used: [(mp.bridge.LancelotHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.LancelotHead, public, public, 0, main.Assignment2, null ), (mp.bridge.LancelotHead, public, private, 3, mp.bridge.LancelotHead, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.LancelotHead, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:5 Number of Methods:13 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:11 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:6 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.8333333333333334 ReadOnly Access Properties Fraction:0.16666666666666666 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:9.0 Average Local References per Variable:5.0 Average Local Assignments per Variable:5.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Component bridge in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Component head in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Component lancelot in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Missing supertype: @Comp301Tags.LOCATABLE of type mp.bridge.LancelotHead[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Super types: mp.bridge.ImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Type mp.bridge.LancelotHead matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:22: Signatures public  getImageFileName:->String common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:27: Signatures public  setImageFileName:String->void common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:28:34: Final parameter newFileName defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:37: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:37: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:42: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:42: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:43:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:44:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:46:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:52: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:52: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:57: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:57: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:58:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:59:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:61:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:67: Signatures public  getWidth:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:67: Signatures public  getWidth:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:72: Signatures public  setWidth:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:73:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:73:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:74:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:76:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:82: Signatures public  getHeight:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:82: Signatures public  getHeight:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:87: Signatures public  setHeight:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:88:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:88:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:89:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:91:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:97: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:102: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:103:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:109: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:109:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component file in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component name in Identifier fileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component value in Identifier value is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Expected getter for property Height of type int in parent type mp.bridge.RobinHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Expected getter for property Width of type int in parent type mp.bridge.RobinHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.bridge.RobinHead[@Comp301Tags.BOUNDED_SHAPE] by methods [public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Expected setter for property Height of type int in parent type mp.bridge.RobinHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Expected setter for property Width of type int in parent type mp.bridge.RobinHead[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RobinHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RobinHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Some method (setImageFileName:String->void) in class mp.bridge.RobinHead:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Access modifiers used: Access Modifiers Used: [(mp.bridge.RobinHead, public, private, 3, mp.bridge.RobinHead, null ), (mp.bridge.RobinHead, public, public, 0, main.Assignment2, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.RobinHead, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:5 Number of Methods:13 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:11 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:6 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.8333333333333334 ReadOnly Access Properties Fraction:0.16666666666666666 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:9.0 Average Local References per Variable:5.5 Average Local Assignments per Variable:5.5 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Component bridge in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Component head in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Component robin in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Missing supertype: @Comp301Tags.LOCATABLE of type mp.bridge.RobinHead[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Super types: mp.bridge.ImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Type mp.bridge.RobinHead matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:21: Signatures public  getImageFileName:->String common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:26: Signatures public  setImageFileName:String->void common with mp.bridge.GuardHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:27:34: Final parameter fileName defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:27:47: 'fileName' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:28:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:36: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:41: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:42:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:43:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:45:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:51: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:56: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:57:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:58:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:60:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:66: Signatures public  getWidth:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:71: Signatures public  setWidth:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:72:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:72:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:73:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:75:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:81: Signatures public  getHeight:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:86: Signatures public  setHeight:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:87:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:87:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:88:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:90:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:96: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:101: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.bridge.ImageShape, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:102:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:108: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:108:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:0: Component limb in Identifier limb is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:1 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Component bridge in Identifier mp.bridge.Scrollable is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Component scrollable in Identifier mp.bridge.Scrollable is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default scroll:String;int;int->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Properties defined: Properties:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Repeated short type name in [javax.swing.Scrollable, mp.bridge.Scrollable], many checks for these types will not work [DuplicateShortTypeName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Scrollable.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component coordinate in Identifier xCoordinate is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component coordinate in Identifier yCoordinate is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component new in Identifier newText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component text in Identifier newText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component text in Identifier text is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component x in Identifier xCoordinate is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component y in Identifier yCoordinate is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Class mp.bridge.SpeechBubble with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Expected getter for property PropertyChangeListeners of type List in parent type mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Expected getter for property X of type int in parent type mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Expected getter for property Y of type int in parent type mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE] by methods [public  setText:String->void, public  setX:int->void, public  setY:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Expected setter for property X of type int in parent type mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Expected setter for property Y of type int in parent type mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between SpeechBubble and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Some method (setText:String->void) in class mp.bridge.SpeechBubble:[@Comp301Tags.LOCATABLE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Access modifiers used: Access Modifiers Used: [(mp.bridge.SpeechBubble, public, private, 3, mp.bridge.SpeechBubble, null ), (mp.bridge.SpeechBubble, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.SpeechBubble, public, public, 0, main.Assignment2, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.SpeechBubble, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:3 Number of Methods:9 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:7 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.75 ReadOnly Access Properties Fraction:0.25 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:7.0 Average Local References per Variable:4.0 Average Local Assignments per Variable:4.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Component bridge in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Component bubble in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Component speech in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Expected interface util.models.PropertyListenerRegisterer of class mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]. Good! [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Interfaces defined: [mp.bridge.StringShape, mp.shapes.Locatable]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getText:->String, public  getX:->int, public  getY:->int, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setText:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Text:String(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Type mp.bridge.SpeechBubble matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:28:25: Final parameter newText defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:37: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:42: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:43:22: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:52: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:57: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:58:22: Final parameter y defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.AvatarImpl not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:67: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.AvatarImpl defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:72: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:73:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.ArthurHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.APolarPoint not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.RotatingLine not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:79:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:0: Component new in Identifier newText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:0: Component text in Identifier newText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Component bridge in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Component shape in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Component string in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getText:->String]Setters:[default setText:String->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package Text:String(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Type mp.bridge.StringShape matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:0: Component left in Identifier left is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:0: Component right in Identifier right is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:1: Expected instantiation of @Comp301Tags.ROTATING_LINE in type mp.bridge.VShape[@Comp301Tags.ANGLE] by methods [public  VShape:->]. Good! [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:1: Missing getter for property LeftLine of type @Comp301Tags.ROTATING_LINE in parent type mp.bridge.VShape[@Comp301Tags.ANGLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:1: Missing getter for property RightLine of type @Comp301Tags.ROTATING_LINE in parent type mp.bridge.VShape[@Comp301Tags.ANGLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:1: Property readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null) common between VShape and mp.shapes.Gorge not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:1: Property readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null) common between VShape and mp.shapes.Gorge not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:3:8: Unused import - mp.shapes.Get. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Access modifiers used: Access Modifiers Used: [(mp.bridge.VShape, public, private, 3, mp.bridge.VShape, null ), (mp.bridge.VShape, public, public, 0, main.Assignment2, null ), (mp.bridge.VShape, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:3.0 Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Component bridge in Identifier mp.bridge.VShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Component shape in Identifier mp.bridge.VShape is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Component v in Identifier mp.bridge.VShape does  not have at least 2 letters [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Component v in Identifier mp.bridge.VShape does not have at least 1 vowels [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Component v in Identifier mp.bridge.VShape is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Expected signature move:int;int->void in type mp.bridge.VShape:[@Comp301Tags.ANGLE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Interfaces defined: [mp.bridge.Angle]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Properties defined: Properties:[readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Type mp.bridge.VShape matches tags (@Comp301Tags.ANGLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:11: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:14: Interface RotateLine used as the type of variable/function left. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:14: Interface RotateLine used as the type of variable/function right. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:22: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:27: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:31: Signatures public  move:int;int->void common with mp.bridge.AvatarImpl defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:31: Signatures public  move:int;int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:32:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:32:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component current in Identifier currentX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component current in Identifier currentY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component x in Identifier currentX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component y in Identifier currentY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Class mp.shapes.APolarPoint with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Expected getter for property PropertyChangeListeners of type List in parent type mp.shapes.APolarPoint[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Expected getter for property X of type int in parent type mp.shapes.APolarPoint[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Expected getter for property Y of type int in parent type mp.shapes.APolarPoint[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.shapes.APolarPoint[@Comp301Tags.LOCATABLE] by methods [public  setX:int->void, public  setY:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Expected setter for property X of type int in parent type mp.shapes.APolarPoint[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Expected setter for property Y of type int in parent type mp.shapes.APolarPoint[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public Angle:double(public , null) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:3 access:public Radius:double(public , null) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Some method (setX:int->void) in class mp.shapes.APolarPoint:[@Comp301Tags.LOCATABLE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Access modifiers used: Access Modifiers Used: [(mp.shapes.APolarPoint, public, private, 3, mp.shapes.APolarPoint, null ), (mp.shapes.APolarPoint, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.APolarPoint, public, public, 0, main.Assignment2, null ), (mp.shapes.APolarPoint, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.APolarPoint, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:2 Number of Methods:9 Number of Functions:5 Number of Non Getter Functions:0 Number of Getters and Setters:7 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:5 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.4 ReadOnly Access Properties Fraction:0.6 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:6.0 Average Local References per Variable:6.666666666666667 Average Local Assignments per Variable:6.666666666666667 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Component a in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Component point in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Component polar in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Component shapes in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Expected interface util.models.PropertyListenerRegisterer of class mp.shapes.APolarPoint[@Comp301Tags.LOCATABLE]. Good! [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Interfaces defined: [mp.shapes.Point, mp.shapes.Locatable, mp.shapes.PolarPointInterface]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getX:->int, public  getY:->int, public  getAngle:->double, public  getRadius:->double, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setX:int->void, public  setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Properties defined: Properties:[readonly  p-v:3 access:public Radius:double(public , null), readonly  p-v:3 access:public Angle:double(public , null), editable, g-s:0 p-v:5 access:public X:int(public ,public ), editable, g-s:0 p-v:5 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Type mp.shapes.APolarPoint matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:18:24: Final parameter theRadius defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:18:48: Final parameter theAngle defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:23:24: Final parameter theX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:23:40: Final parameter theY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:28: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:28: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:28: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:28: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:28: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:28: Signatures public  getX:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:28: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:33: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:33: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:33: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:33: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:33: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:33: Signatures public  getY:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:33: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:38: Signatures public  getAngle:->double common with mp.shapes.RotatingLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:43: Signatures public  getRadius:->double common with mp.shapes.RotatingLine not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:48: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:48: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:48: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:48: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:48: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:48: Signatures public  setX:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:48: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:49:22: Final parameter x defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:51:13: Variable 'currentY' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:60: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:60: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:60: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:60: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:60: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:60: Signatures public  setY:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:60: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:61:22: Final parameter y defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:63:13: Variable 'currentX' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:72: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:72: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:72: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:72: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:72: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:72: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:72: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:77: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:77: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:77: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:77: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:77: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:77: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:77: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:78:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.ArthurHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.RotatingLine not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:84:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component conversion in Identifier percentConversion is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component height in Identifier newHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component new in Identifier newHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component new in Identifier newWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component new in Identifier newX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component new in Identifier newY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component percent in Identifier percentConversion is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component percentage in Identifier percentage is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component width in Identifier newWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component x in Identifier newX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component y in Identifier newY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Class mp.shapes.AScalableRectangle with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Expected getter for property Height of type int in parent type mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Expected getter for property Width of type int in parent type mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE] by methods [public  setHeight:int->void, public  setWidth:int->void, public  setX:int->void, public  setY:int->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Expected setter for property Height of type int in parent type mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Expected setter for property Width of type int in parent type mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between AScalableRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between AScalableRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between AScalableRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between AScalableRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between AScalableRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between AScalableRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between AScalableRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between AScalableRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between AScalableRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between AScalableRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between AScalableRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between AScalableRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between AScalableRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between AScalableRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between AScalableRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: Some method (setHeight:int->void) in class mp.shapes.AScalableRectangle:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:1: The following public methods do not override: [public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Access modifiers used: Access Modifiers Used: [(mp.shapes.AScalableRectangle, public, public, 0, main.Assignment2, null ), (mp.shapes.AScalableRectangle, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AScalableRectangle, public, private, 3, mp.shapes.AScalableRectangle, null ), (mp.shapes.AScalableRectangle, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:4 Number of Methods:12 Number of Functions:5 Number of Non Getter Functions:0 Number of Getters and Setters:9 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:6 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:5 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.8 ReadOnly Access Properties Fraction:0.2 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:5.0 Average Local References per Variable:5.0 Average Local Assignments per Variable:5.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Component a in Identifier mp.shapes.AScalableRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Component rectangle in Identifier mp.shapes.AScalableRectangle is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Component scalable in Identifier mp.shapes.AScalableRectangle is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Component shapes in Identifier mp.shapes.AScalableRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Interfaces defined: [mp.shapes.AScalableRectangleInterface, mp.shapes.BoundedShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void, public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setHeight:int->void, public  setWidth:int->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Missing supertype: @Comp301Tags.LOCATABLE of type mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Type mp.shapes.AScalableRectangle matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:11: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:18:31: Final parameter initialX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:18:51: Final parameter initialY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:18:71: Final parameter initialWidth defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:18:95: Final parameter initialHeight defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:24: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:24: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:24: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:29: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:29: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:29: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:34: Signatures public  getWidth:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:34: Signatures public  getWidth:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:34: Signatures public  getWidth:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:39: Signatures public  getHeight:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:39: Signatures public  getHeight:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:39: Signatures public  getHeight:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:44: Signatures public  setHeight:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:44: Signatures public  setHeight:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:44: Signatures public  setHeight:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:45:27: Final parameter newHeight defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:54: Signatures public  setWidth:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:54: Signatures public  setWidth:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:54: Signatures public  setWidth:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:55:26: Final parameter newWidth defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:65:23: Final parameter percentage defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:70: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:70: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:70: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:71:22: Final parameter newX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:80: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:80: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:80: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:81:22: Final parameter newY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:90: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:90: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:90: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:95: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:95: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:95: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:96:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:102: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:102: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:102: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:102:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:0: Component percentage in Identifier percentage is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Access modifiers used: Access Modifiers Used: [(mp.shapes.AScalableRectangleInterface, public, public, 0, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:1.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Component a in Identifier mp.shapes.AScalableRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Component interface in Identifier mp.shapes.AScalableRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Component rectangle in Identifier mp.shapes.AScalableRectangleInterface is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Component scalable in Identifier mp.shapes.AScalableRectangleInterface is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Component shapes in Identifier mp.shapes.AScalableRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void]Getters:[]Setters:[public  setHeight:int->void, public  setWidth:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Properties defined: Properties:[writeonly  p-v:5 access:public Height:int( null,public ), writeonly  p-v:5 access:public Width:int( null,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Super types: [mp.shapes.BoundedShape] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Type mp.shapes.AScalableRectangleInterface matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangleInterface.java:5: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Access modifiers used: Access Modifiers Used: [(mp.shapes.BoundedShape, public, public, 0, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Component bounded in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Component shape in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Component shapes in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getWidth:->int, default getHeight:->int]Setters:[default setWidth:int->void, default setHeight:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package Height:int(default ,default ), editable, g-s:0 p-v:5 access:package Width:int(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Type mp.shapes.BoundedShape matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Access modifiers used: Access Modifiers Used: [(mp.shapes.Get, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Component get in Identifier mp.shapes.Get is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Component shapes in Identifier mp.shapes.Get is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Properties defined: Properties:[readonly  p-v:5 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:5 access:public LeftLine:mp.shapes.RotateLine(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Type mp.shapes.Get matches tags (@Comp301Tags.ANGLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:11: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:12: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:1 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Component get in Identifier mp.shapes.GetRect is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Component rect in Identifier mp.shapes.GetRect is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Component shapes in Identifier mp.shapes.GetRect is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getRectangle:->mp.shapes.AScalableRectangleInterface]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Properties defined: Properties:[readonly  p-v:5 access:public Rectangle:mp.shapes.AScalableRectangleInterface(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Super types: [mp.shapes.Get] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:4: Interface AScalableRectangleInterface used as the type of variable/function getRectangle. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component c in Identifier c1 does  not have at least 2 letters [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component c in Identifier c1 does not have at least 1 vowels [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component c in Identifier c1 is not in dictionary [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component c in Identifier c2 does  not have at least 2 letters [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component c in Identifier c2 does not have at least 1 vowels [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component c in Identifier c2 is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component left in Identifier leftLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component line in Identifier leftLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component line in Identifier rightLine is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component lineheight in Identifier lineheight is not in dictionary [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component linetopy in Identifier linetopy is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component lower in Identifier lower is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component rectangle in Identifier rectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component right in Identifier rightLine is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component rightlinex in Identifier rightlinex is not in dictionary [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component upper in Identifier upper is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: Property readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null) common between Gorge and mp.bridge.VShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: Property readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null) common between Gorge and mp.bridge.VShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Access modifiers used: Access Modifiers Used: [(mp.shapes.Gorge, public, private, 3, mp.shapes.Gorge, null ), (mp.shapes.Gorge, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:10 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.8 Average Local Assignments per Variable:2.8 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Component gorge in Identifier mp.shapes.Gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Component shapes in Identifier mp.shapes.Gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Interfaces defined: [mp.shapes.GetRect]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine, public  getRectangle:->mp.shapes.AScalableRectangleInterface]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Properties defined: Properties:[readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public Rectangle:mp.shapes.AScalableRectangleInterface(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:15: Interface RotateLine used as the type of variable/function leftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:16: Interface RotateLine used as the type of variable/function rightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:17: Interface AScalableRectangleInterface used as the type of variable/function rectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:18:18: Final parameter x defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:35: Signatures public  getLeftLine:->mp.shapes.RotateLine common with mp.bridge.VShape not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:36: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:39: Signatures public  getRightLine:->mp.shapes.RotateLine common with mp.bridge.VShape not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:40: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:44: Interface AScalableRectangleInterface used as the type of variable/function getRectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Access modifiers used: Access Modifiers Used: [(mp.shapes.Locatable, public, public, 0, main.Assignment2, null ), (mp.shapes.Locatable, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.Locatable, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:5 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:5 Number of Non Public Methods:5 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:3 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.6666666666666666 ReadOnly Access Properties Fraction:0.3333333333333333 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Component locatable in Identifier mp.shapes.Locatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Component shapes in Identifier mp.shapes.Locatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getX:->int, default getY:->int, default getPropertyChangeListeners:->java.util.List]Setters:[default setX:int->void, default setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package X:int(default ,default ), editable, g-s:0 p-v:5 access:package Y:int(default ,default ), readonly  p-v:5 access:package PropertyChangeListeners:java.util.List(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Super types: [util.models.PropertyListenerRegisterer] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Type mp.shapes.Locatable matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.Moveable, public, protected, 1, mp.bridge.VShape, null ), (mp.shapes.Moveable, public, public, 0, main.Assignment2, null ), (mp.shapes.Moveable, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Component moveable in Identifier mp.shapes.Moveable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Component shapes in Identifier mp.shapes.Moveable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Access modifiers used: Access Modifiers Used: [(mp.shapes.Point, public, public, 0, main.Assignment2, null ), (mp.shapes.Point, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.Point, public, package, 2, mp.shapes.RotatingLine, null ), (mp.shapes.Point, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Component point in Identifier mp.shapes.Point is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Component shapes in Identifier mp.shapes.Point is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getX:->int, public  getY:->int, public  getAngle:->double, public  getRadius:->double]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Properties defined: Properties:[readonly  p-v:5 access:public Radius:double(public , null), readonly  p-v:5 access:public Angle:double(public , null), readonly  p-v:5 access:public X:int(public , null), readonly  p-v:5 access:public Y:int(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Point.java:5: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Component interface in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Component point in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Component polar in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Component shapes in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getAngle:->double, public  getRadius:->double]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Properties defined: Properties:[readonly  p-v:5 access:public Radius:double(public , null), readonly  p-v:5 access:public Angle:double(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Type mp.shapes.PolarPointInterface matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component units in Identifier units is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Access modifiers used: Access Modifiers Used: [(mp.shapes.RotateLine, public, public, 0, main.Assignment2, null ), (mp.shapes.RotateLine, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.RotateLine, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:3 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.3333333333333333 WriteOnly  Properties Fraction:0.6666666666666666 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Component line in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Component rotate in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Component shapes in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default rotate:int->void]Getters:[default getHeight:->int]Setters:[default setRadius:double->void, default setAngle:double->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Properties defined: Properties:[writeonly  p-v:5 access:package Radius:double( null,default ), writeonly  p-v:5 access:package Angle:double( null,default ), readonly  p-v:5 access:package Height:int(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Super types: [mp.shapes.BoundedShape, mp.shapes.Moveable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Type mp.shapes.RotateLine matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:7: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component offset in Identifier xOffset is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component offset in Identifier yOffset is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component old in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component point in Identifier point is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component unit in Identifier UNIT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component units in Identifier units is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component value in Identifier oldValue is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component x in Identifier xOffset is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component y in Identifier yOffset is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Setter setHeight does not assign to a global variable [SetterAssignsGlobal]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Setter setWidth does not assign to a global variable [SetterAssignsGlobal]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property Angle of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property Radius of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property X of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property Y of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property Angle of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property Radius of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property X of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property Y of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Angle:double(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RotatingLine and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Radius:double(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RotatingLine and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.bridge.AvatarImpl not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null) common between RotatingLine and mp.shapes.AScalableRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: The following public methods do not override: [public  getRadius:->double, public  getAngle:->double, public  getXProperty:->int, public  setXProperty:int->void, public  getYProperty:->int, public  setYProperty:int->void, public  getRadiusProperty:->double, public  setRadiusProperty:double->void, public  getAngleProperty:->double, public  setAngleProperty:double->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Access modifiers used: Access Modifiers Used: [(mp.shapes.RotatingLine, public, public, 0, main.Assignment2, null ), (mp.shapes.RotatingLine, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.RotatingLine, public, private, 3, mp.shapes.RotatingLine, null ), (mp.shapes.RotatingLine, public, public, 0, main.ConsoleSceneViewImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:2 Number of Methods:25 Number of Functions:11 Number of Non Getter Functions:0 Number of Getters and Setters:21 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:5 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:11 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.9090909090909091 ReadOnly Access Properties Fraction:0.09090909090909091 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:3.5 Average Local References per Variable:5.8 Average Local Assignments per Variable:5.8 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Component line in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Component rotating in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Component shapes in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Expected signature move:int;int->void in type mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Expected signature rotate:int->void in type mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Interfaces defined: [mp.shapes.RotateLine]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  rotate:int->void, public  move:int;int->void, public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  removePropertyChangeListener:java.beans.PropertyChangeListener->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getRadius:->double, public  getAngle:->double, public  getXProperty:->int, public  getYProperty:->int, public  getRadiusProperty:->double, public  getAngleProperty:->double, public  getPropertyChangeListeners:->java.util.List]Setters:[public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void, public  setRadius:double->void, public  setAngle:double->void, public  setXProperty:int->void, public  setYProperty:int->void, public  setRadiusProperty:double->void, public  setAngleProperty:double->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public Radius:double(public ,public ), editable, g-s:0 p-v:5 access:public XProperty:int(public ,public ), editable, g-s:0 p-v:3 access:public Angle:double(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), editable, g-s:0 p-v:5 access:public AngleProperty:double(public ,public ), readonly  p-v:3 access:public PropertyChangeListeners:java.util.List(public , null), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:5 access:public YProperty:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public ), editable, g-s:0 p-v:5 access:public RadiusProperty:double(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Type mp.shapes.RotatingLine matches tags (@Comp301Tags.ROTATING_LINE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:15: Interface Point used as the type of variable/function point. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:17:33: Named Constant UNIT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25: Signatures public  getX:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25: Signatures public  getX:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:30: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:30: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:30: Signatures public  setX:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:30: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:30: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:30: Signatures public  setX:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:31:22: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:40: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:40: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:40: Signatures public  getY:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:40: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:40: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:40: Signatures public  getY:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:45: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:45: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:45: Signatures public  setY:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:45: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:45: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:45: Signatures public  setY:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:46:22: Final parameter y defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:54: Signatures public  getWidth:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:54: Signatures public  getWidth:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:54: Signatures public  getWidth:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:54: Signatures public  getWidth:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:54: Signatures public  getWidth:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:54: Signatures public  getWidth:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:58: Signatures public  getHeight:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:58: Signatures public  getHeight:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:58: Signatures public  getHeight:->int common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:58: Signatures public  getHeight:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:58: Signatures public  getHeight:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:58: Signatures public  getHeight:->int common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63: Signatures public  setWidth:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63: Signatures public  setWidth:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63: Signatures public  setWidth:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63: Signatures public  setWidth:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63: Signatures public  setWidth:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63: Signatures public  setWidth:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:64:26: Final parameter width defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69: Signatures public  setHeight:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69: Signatures public  setHeight:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69: Signatures public  setHeight:int->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69: Signatures public  setHeight:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69: Signatures public  setHeight:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69: Signatures public  setHeight:int->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:70:27: Final parameter height defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:79:27: Final parameter radius defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:88:26: Final parameter angle defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:93:24: Final parameter units defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:98:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:98:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:108:30: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:116:30: Final parameter y defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:124:35: Final parameter radius defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:132:34: Final parameter angle defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:137: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:137: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:137: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:137: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:137: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:137: Signatures public  getPropertyChangeListeners:->java.util.List common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:142: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:142: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:142: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:142: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:142: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:142: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:143:43: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:149: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.ArthurHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:149: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GalahadHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:149: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.GuardHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:149: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.LancelotHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:149: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.RobinHead not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:149: Signatures public  removePropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AScalableRectangle not defined in a common type  [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:149:46: Final parameter listener defined. Good! [FinalParameterDefined]
Audit done.
