
//SESSION START
0,Wed Jul 09 20:04:16 EDT 2025,48787
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fileName = "images/lancelot.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public LancelotHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String newFileName) {
        final String oldValue = this.fileName;
        this.fileName = newFileName;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, newFileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X_COORDINATE = 10;
    public static final int SOME_Y_COORDINATE = 50;
    public static final int LANCELOT_CONSTANT = 8;
    public static final int ROBIN_CONSTANT = 15;
    public static final int GALAHAD_CONSTANT = 22;
    public static final int GUARD_CONSTANT = 30;
    private Gorge gorge;
    private Avatar currentAvatar;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X_COORDINATE = 500;
    private static final int KNIGHT_Y_COORDINATE = 600; 
    private static final int GUARD_Y_COORDINATE = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X_COORDINATE = 750;
    private static int gorgeYCoordinate = 0;
    int difference = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X_COORDINATE, SOME_Y_COORDINATE);
      lancelot.move(SOME_X_COORDINATE*LANCELOT_CONSTANT, SOME_Y_COORDINATE);
      robin.move(SOME_X_COORDINATE*ROBIN_CONSTANT, SOME_Y_COORDINATE);
      galahad.move(SOME_X_COORDINATE*GALAHAD_CONSTANT,SOME_Y_COORDINATE);
      guard.move(AREA_X_COORDINATE,GUARD_Y_COORDINATE);
      gorge = new Gorge(GORGE_X_COORDINATE);
      knightArea = new AScalableRectangle(AREA_X_COORDINATE,KNIGHT_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X_COORDINATE,GUARD_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
    }

    // Required getters for BRIDGE_SCENE
    public Avatar getArthur() {
        return arthur;
    }

    public Avatar getLancelot() {
        return lancelot;
    }

    public Avatar getRobin() {
        return robin;
    }

    public Avatar getGalahad() {
        return galahad;
    }

    public Avatar getGuard() {
        return guard;
    }

    // Additional getters for other properties
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }

    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }

    public Gorge getGorge() {
        return gorge;
    }

    public boolean getOccupied() {
        return occupied;
    }

    public boolean getKnightTurn() {
        return knightTurn;
    }

    @Override
    public void passed() {
    	if(!knightTurn){
    		currentAvatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);
    		occupied = false;
    	}
    }
    @Override
    public void failed() {
        if (!occupied) {
            return;
        }

        if (!knightTurn) {
            currentAvatar.getHead().setX(GORGE_X_COORDINATE);
            currentAvatar.getHead().setY(gorgeYCoordinate);
            gorgeYCoordinate += difference;
            occupied = !occupied;
        } else {
            guard.getHead().setX(GORGE_X_COORDINATE);
            guard.getHead().setY(gorgeYCoordinate);
            gorgeYCoordinate += difference;
        }
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);}
    	occupied = true;
    	currentAvatar = avatar;
    }
    @Override
    public void say(final String speechText) {
        if (!occupied) {
            return;
        }

        if (!knightTurn) {
            guard.getStringShape().setText(speechText);
            knightTurn = !knightTurn;
        } else {
            currentAvatar.getStringShape().setText(speechText);
            knightTurn = !knightTurn;
        }
    }

    @Override
    public void scroll(final String limb, final int deltaX, final int deltaY) {
        currentAvatar.scroll(limb, deltaX, deltaY);
    }
}
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape, Locatable {
    private String text = "Grail";
    private int xCoordinate, yCoordinate;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public SpeechBubble() {
    }
    @Override
    public String getText() {
        return text;
    }

    @Override
    public void setText(final String newText) {
        final String oldValue = this.text;
        this.text = newText;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "text", oldValue, newText);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return xCoordinate;
    }

    @Override
    public void setX(final int x) {
        final int oldValue = this.xCoordinate;
        this.xCoordinate = x;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return yCoordinate;
    }

    @Override
    public void setY(final int y) {
        final int oldValue = this.yCoordinate;
        this.yCoordinate = y;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

//END OF FILE
//START OF FILE: mp/shapes/Moveable.java
package mp.shapes;

public interface Moveable {
public void move(int deltaX, int deltaY);
}

//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fileName = "images/guard.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public GuardHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface, BoundedShape {
    private int x, y, width, height;
    private final int percentConversion = 100;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public AScalableRectangle(final int initialX, final int initialY, final int initialWidth, final int initialHeight) {
        this.x = initialX;
        this.y = initialY;
        this.width = initialWidth;
        this.height = initialHeight;
    }
    @Override
    public int getX() {
        return x;
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int newHeight) {
        final int oldValue = this.height;
        this.height = newHeight;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, newHeight);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setWidth(final int newWidth) {
        final int oldValue = this.width;
        this.width = newWidth;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, newWidth);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void scale(final int percentage) {
        width = (width * percentage) / percentConversion;
        height = (height * percentage) / percentConversion;
    }

    @Override
    public void setX(final int newX) {
        final int oldValue = this.x;
        this.x = newX;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, newX);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setY(final int newY) {
        final int oldValue = this.y;
        this.y = newY;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, newY);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine extends BoundedShape, Moveable{
    void setRadius(double radius);
    void setAngle(double angle);
    void rotate(int units);
    int getHeight();
}
//END OF FILE
//START OF FILE: main/ConsoleSceneView.java
package main;

import java.beans.PropertyChangeListener;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public interface ConsoleSceneView extends PropertyChangeListener {
}

//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String newFileName) {
        final String oldValue = this.fileName;
        this.fileName = newFileName;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, newFileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        final int oldValue = this.x;
        this.x = value;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        final int oldValue = this.y;
        this.y = value;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int newWidth) {
        final int oldValue = this.width;
        this.width = newWidth;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, newWidth);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int newHeight) {
        final int oldValue = this.height;
        this.height = newHeight;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, newHeight);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fileName = "images/galahad.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public GalahadHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

//END OF FILE
//START OF FILE: main/RunSS25A2Tests.java
package main;

import grader.basics.execution.BasicProjectExecution;
import gradingTools.comp301ss24.assignment2.SS24Assignment2Suite;
import trace.grader.basics.GraderBasicsTraceUtility;
public class RunSS25A2Tests {
	 private static final int MAX_PRINTED_TRACES = 600;
		 private static final int MAX_TRACES         = 2000;
		 private static final int PROCESS_TIMEOUT_S  = 5;
		public static void main(String[] args) {
			// if you set this to false, grader steps will not be traced
			GraderBasicsTraceUtility.setTracerShowInfo(true);	
			// if you set this to false, all grader steps will be traced,
			// not just the ones that failed		
			GraderBasicsTraceUtility.setBufferTracedMessages(true);
			// Change this number if a test trace gets longer than 600 and is clipped
			GraderBasicsTraceUtility.setMaxPrintedTraces(MAX_PRINTED_TRACES);
			// Change this number if all traces together are longer than 2000
			GraderBasicsTraceUtility.setMaxTraces(MAX_TRACES);
			// Change this number if your process times out prematurely
			BasicProjectExecution.setProcessTimeOut(PROCESS_TIMEOUT_S);
			// You need to always call such a method
		SS24Assignment2Suite.main(args);
	}
}

//END OF FILE
//START OF FILE: mp/shapes/Get.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.ANGLE})
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Get {
 public RotateLine getLeftLine();
 public RotateLine getRightLine();
 
}

//END OF FILE
//START OF FILE: main/ConsoleSceneViewImpl.java
package main;

import java.beans.PropertyChangeEvent;
import java.util.List;
import mp.bridge.Avatar;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public class ConsoleSceneViewImpl implements ConsoleSceneView {
    
    private static ConsoleSceneView instance;

    private ConsoleSceneViewImpl() {
        final BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        for (Avatar knight : List.of(
                scene.getArthur(),
                scene.getGalahad(),
                scene.getLancelot(),
                scene.getRobin(),
                scene.getGuard())) {
            knight.getHead().addPropertyChangeListener(this);
            knight.getStringShape().addPropertyChangeListener(this);
            knight.getArms().getLeftLine().addPropertyChangeListener(this);
            knight.getArms().getRightLine().addPropertyChangeListener(this);
            knight.getLegs().getLeftLine().addPropertyChangeListener(this);
            knight.getLegs().getRightLine().addPropertyChangeListener(this);
        }
    }

    public static ConsoleSceneView getInstance() {
        if (instance == null) {
            instance = new ConsoleSceneViewImpl();
        }
        return instance;
    }

    @Override
    public void propertyChange(final PropertyChangeEvent evt) {
        System.out.println(evt);
    }
}

//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fileName = "images/robin.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    public RobinHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

//END OF FILE
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class Gorge implements GetRect{
    int rightlinex = 950;
    int linetopy = 0;
    int lineheight = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangleInterface rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(lineheight);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(linetopy);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(lineheight);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(rightlinex);
        rightLine.setY(linetopy);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, rightlinex - x, lower);
    }
    @Override
    public RotateLine getLeftLine() {
    	return leftLine;
    }
    @Override
    public RotateLine getRightLine(){
    	return rightLine;
    }
    @Override
    public AScalableRectangleInterface getRectangle() {
    	return rectangle;
    }
}
//END OF FILE
//START OF FILE: mp/shapes/Locatable.java
package mp.shapes;

import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import util.models.PropertyListenerRegisterer;

@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface Locatable extends PropertyListenerRegisterer {
    int getX();
    void setX(final int x);
    int getY();
    void setY(final int y);
    List getPropertyChangeListeners();
}

//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene extends javax.swing.Scrollable {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    void passed();
    void failed();
    void approach(final Avatar avatar);
    void say(final String speechText);
    AScalableRectangleInterface getKnightArea();
    AScalableRectangleInterface getGuardArea();
    Gorge getGorge();
    boolean getOccupied();
    boolean getKnightTurn();
    void scroll(String limb, int deltaX, int deltaY);
}

//END OF FILE
//START OF FILE: mp/bridge/Scrollable.java
package mp.bridge;

public interface Scrollable {
    void scroll(String limb, int deltaX, int deltaY);
}
//END OF FILE
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.POINT_PATTERN)
public class APolarPoint implements Point, Locatable, PolarPointInterface {
    private double radius, angle;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public APolarPoint(final double theRadius, final double theAngle) {
        radius = theRadius;
        angle = theAngle;
    }

    public APolarPoint(final int theX, final int theY) {
        radius = Math.sqrt(theX * theX + theY * theY);
        angle = Math.atan((double) theY / theX);
    }

    @Override
    public int getX() {
        return (int) (radius * Math.cos(angle));
    }

    @Override
    public int getY() {
        return (int) (radius * Math.sin(angle));
    }

    @Override
    public double getAngle() {
        return angle;
    }

    @Override
    public double getRadius() {
        return radius;
    }

    @Override
    public void setX(final int x) {
        // Convert to polar coordinates and update
        int currentY = getY();
        radius = Math.sqrt(x * x + currentY * currentY);
        angle = Math.atan2(currentY, x);
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", getX(), x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setY(final int y) {
        // Convert to polar coordinates and update
        int currentX = getX();
        radius = Math.sqrt(currentX * currentX + y * y);
        angle = Math.atan2(y, currentX);
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", getY(), y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public interface AScalableRectangleInterface extends BoundedShape{
	public void scale(int percentage);
	public void setHeight(int x);
	public void setWidth(int x);
}

//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int x, int y);
}

//END OF FILE
//START OF FILE: mp/shapes/GetRect.java
package mp.shapes;

public interface GetRect extends Get{
	public AScalableRectangleInterface getRectangle();
}

//END OF FILE
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public interface StringShape extends Locatable{
    String getText();
    void setText(String newText);
}

//END OF FILE
//START OF FILE: main/Assignment2.java
package main;

import bus.uigen.OEFrame;
import bus.uigen.ObjectEditor;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import mp.bridge.BridgeScene;

public class Assignment2 {
    public static final int SOME_RADIUS = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int DELTA = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        final RotateLine line = new RotatingLine();
        line.setRadius(SOME_RADIUS);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        final OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(DELTA, DELTA);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }
    public static void main(final String[] args) throws InterruptedException {
    	final BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
        animateLine();
    }
}

//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;

import mp.shapes.Get;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotateLine left, right;

    public VShape() {
        left = new RotatingLine();
        right = new RotatingLine();
    }

    @Override
    public RotateLine getLeftLine() {
        return left;
    }

    @Override
    public RotateLine getRightLine() {
        return right;
    }

    @Override
    public void move(final int deltaX, final int deltaY) {
        left.move(deltaX, deltaY);
        right.move(deltaX, deltaY);
    }
}

//END OF FILE
//START OF FILE: mp/shapes/BoundedShape.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface BoundedShape extends Locatable {
    int getWidth();
    void setWidth(final int width);
    int getHeight();
    void setHeight(final int height);
}
//END OF FILE
//START OF FILE: main/StaticFactoryClass.java
package main;

import mp.bridge.Angle;
import mp.bridge.BridgeScene;
import mp.bridge.VShape;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass {
    private static BridgeScene scene;

    public static BridgeScene bridgeSceneFactoryMethod() {
        if (scene == null) {
            scene = new BridgeSceneImpl();
        }
        return scene;
    }

    public static ConsoleSceneView consoleSceneViewFactoryMethod() {
        return ConsoleSceneViewImpl.getInstance();
    }

    public static Angle legsFactoryMethod() {
        return new VShape();
    }

    // Placeholder methods for other required factory methods
    public static Object inheritingBridgeScenePainterFactoryMethod() {
        return null; // TODO: Implement when class is available
    }

    public static Object observableBridgeScenePainterFactoryMethod() {
        return null; // TODO: Implement when class is available
    }

    public static Object delegatingBridgeSceneViewFactoryMethod() {
        return null; // TODO: Implement when class is available
    }

    public static Object bridgeSceneControllerFactoryMethod() {
        return null; // TODO: Implement when class is available
    }
}
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import javax.swing.Scrollable;

import mp.shapes.Moveable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable, Scrollable {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int deltaX, int deltaY);
    void scale(double factor);
	void scroll(String limb, int dx, int dy);
}

//END OF FILE
//START OF FILE: mp/shapes/PolarPointInterface.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.POINT_PATTERN)
public interface PolarPointInterface extends Locatable{
	public double getAngle();
	public double getRadius();
}

//END OF FILE
//START OF FILE: mp/shapes/Point.java
package mp.shapes;
import util.annotations.Explanation;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@StructurePattern(StructurePatternNames.POINT_PATTERN)
@Explanation("Location in Java coordinate System.")
public interface Point {
	public int getX(); 
	public int getY(); 	
	public double getAngle(); 
	public double getRadius(); 
}

//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import java.beans.PropertyChangeListener;
import java.util.List;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Moveable;
import util.models.PropertyListenerRegisterer;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable, PropertyListenerRegisterer {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }

    private void layoutAtOrigin() {}

    @Override
    public ImageShape getHead() { return head; }

    @Override
    public StringShape getStringShape() { return speech; }

    @Override
    public Angle getArms() { return arms; }

    @Override
    public Angle getLegs() { return legs; }

    @Override
    public void move(final int deltaX, final int deltaY) {
        head.setX(head.getX() + deltaX);
        head.setY(head.getY() + deltaY);
        arms.move(deltaX, deltaY);
        legs.move(deltaX, deltaY);
        speech.setX(speech.getX() + deltaX);
        speech.setY(speech.getY() + deltaY);
        layoutAtOrigin();
    }

    @Override
    public void scroll(final String limb, final int deltaX, final int deltaY) {
        if ("left arm".equalsIgnoreCase(limb)) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + deltaX);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + deltaY);
        } else if ("right arm".equalsIgnoreCase(limb)) {
            arms.getRightLine().setX(arms.getRightLine().getX() + deltaX);
            arms.getRightLine().setY(arms.getRightLine().getY() + deltaY);
        } else if ("left leg".equalsIgnoreCase(limb)) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + deltaX);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + deltaY);
        } else if ("right leg".equalsIgnoreCase(limb)) {
            legs.getRightLine().setX(legs.getRightLine().getX() + deltaX);
            legs.getRightLine().setY(legs.getRightLine().getY() + deltaY);
        }
    }

    public void scale(final double factor) {
        head.setWidth((int) (head.getWidth() * factor));
        head.setHeight((int) (head.getHeight() * factor));
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        head.addPropertyChangeListener(listener);
        speech.addPropertyChangeListener(listener);
        // if arms/legs support property listening:
        arms.getLeftLine().addPropertyChangeListener(listener);
        arms.getRightLine().addPropertyChangeListener(listener);
        legs.getLeftLine().addPropertyChangeListener(listener);
        legs.getRightLine().addPropertyChangeListener(listener);
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return head.getPropertyChangeListeners(); // or merged list if needed
    }
}

//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.ROTATING_LINE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int xOffset, yOffset;
    private static final double UNIT = Math.PI / 32;

    public RotatingLine() {
        this.xOffset = 0;
        this.yOffset = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return xOffset + point.getX();
    }

    @Override
    public void setX(final int x) {
        xOffset = x;
    }

    @Override
    public int getY() {
        return yOffset + point.getY();
    }

    @Override
    public void setY(final int y) {
        yOffset = y;
    }
    @Override
    public int getWidth() {
        return point.getX();
    }
    @Override
    public int getHeight() {
        return point.getY();
    }
    @Override
    public double getRadius() {
        return point.getRadius();
    }

    @Override
    public void setRadius(final double radius) {
        point = new APolarPoint(radius, point.getAngle());
    }

    @Override
    public double getAngle() {
        return point.getAngle();
    }

    @Override
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    @Override
    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override
    public void move(final int deltaX, final int deltaY) {
        setX(xOffset + deltaX);
        setY(yOffset + deltaY);
    }

    // Additional getters/setters for ROTATING_LINE requirements
    public int getXProperty() {
        return getX();
    }

    public void setXProperty(final int x) {
        setX(x);
    }

    public int getYProperty() {
        return getY();
    }

    public void setYProperty(final int y) {
        setY(y);
    }

    public double getRadiusProperty() {
        return getRadius();
    }

    public void setRadiusProperty(final double radius) {
        setRadius(radius);
    }

    public double getAngleProperty() {
        return getAngle();
    }

    public void setAngleProperty(final double angle) {
        setAngle(angle);
    }
}

//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;

import mp.shapes.BoundedShape;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public abstract class ImageShape implements BoundedShape {
    public abstract String getImageFileName();
    public abstract void setImageFileName(final String fileName);
}

//END OF FILE

//SESSION END

//SESSION START
1,Wed Jul 09 20:13:42 EDT 2025,2984
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead extends ImageShape {
    private String fileName = "images/lancelot.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public LancelotHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String newFileName) {
        final String oldValue = this.fileName;
        this.fileName = newFileName;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, newFileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=318	-4	+BOUND	=1	+D	=1	-1	+SH	=1	-2	+P	=1	-2	=83	-4	=1	-1	+xt	=2	-1	+d	=2768	-14	=145
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String newFileName) {
        final String oldValue = this.fileName;
        this.fileName = newFileName;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, newFileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        final int oldValue = this.x;
        this.x = value;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        final int oldValue = this.y;
        this.y = value;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int newWidth) {
        final int oldValue = this.width;
        this.width = newWidth;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, newWidth);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int newHeight) {
        final int oldValue = this.height;
        this.height = newHeight;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, newHeight);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=318	-4	+BOUND	=1	+D	=1	-1	+SH	=1	-2	+P	=1	-2	=2917	-14	=145
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead extends ImageShape {
    private String fileName = "images/galahad.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public GalahadHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=318	-4	+BOUND	=1	+D	=1	-1	+SH	=1	-2	+P	=1	-2	=82	-4	=1	-1	+xt	=2	-1	+d	=2745	-14	=145
//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X_COORDINATE = 10;
    public static final int SOME_Y_COORDINATE = 50;
    public static final int LANCELOT_CONSTANT = 8;
    public static final int ROBIN_CONSTANT = 15;
    public static final int GALAHAD_CONSTANT = 22;
    public static final int GUARD_CONSTANT = 30;
    private Gorge gorge;
    private Avatar currentAvatar;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X_COORDINATE = 500;
    private static final int KNIGHT_Y_COORDINATE = 600; 
    private static final int GUARD_Y_COORDINATE = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X_COORDINATE = 750;
    private static int gorgeYCoordinate = 0;
    int difference = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X_COORDINATE, SOME_Y_COORDINATE);
      lancelot.move(SOME_X_COORDINATE*LANCELOT_CONSTANT, SOME_Y_COORDINATE);
      robin.move(SOME_X_COORDINATE*ROBIN_CONSTANT, SOME_Y_COORDINATE);
      galahad.move(SOME_X_COORDINATE*GALAHAD_CONSTANT,SOME_Y_COORDINATE);
      guard.move(AREA_X_COORDINATE,GUARD_Y_COORDINATE);
      gorge = new Gorge(GORGE_X_COORDINATE);
      knightArea = new AScalableRectangle(AREA_X_COORDINATE,KNIGHT_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X_COORDINATE,GUARD_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
    }

    // Required getters for BRIDGE_SCENE
    public Avatar getArthur() {
        return arthur;
    }

    public Avatar getLancelot() {
        return lancelot;
    }

    public Avatar getRobin() {
        return robin;
    }

    public Avatar getGalahad() {
        return galahad;
    }

    public Avatar getGuard() {
        return guard;
    }

    // Additional getters for other properties
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }

    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }

    public Gorge getGorge() {
        return gorge;
    }

    public boolean getOccupied() {
        return occupied;
    }

    public boolean getKnightTurn() {
        return knightTurn;
    }

    @Override
    public void passed() {
    	if(!knightTurn){
    		currentAvatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);
    		occupied = false;
    	}
    }
    @Override
    public void failed() {
        if (!occupied) {
            return;
        }

        if (!knightTurn) {
            currentAvatar.getHead().setX(GORGE_X_COORDINATE);
            currentAvatar.getHead().setY(gorgeYCoordinate);
            gorgeYCoordinate += difference;
            occupied = !occupied;
        } else {
            guard.getHead().setX(GORGE_X_COORDINATE);
            guard.getHead().setY(gorgeYCoordinate);
            gorgeYCoordinate += difference;
        }
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);}
    	occupied = true;
    	currentAvatar = avatar;
    }
    @Override
    public void say(final String speechText) {
        if (!occupied) {
            return;
        }

        if (!knightTurn) {
            guard.getStringShape().setText(speechText);
            knightTurn = !knightTurn;
        } else {
            currentAvatar.getStringShape().setText(speechText);
            knightTurn = !knightTurn;
        }
    }

    @Override
    public void scroll(final String limb, final int deltaX, final int deltaY) {
        currentAvatar.scroll(limb, deltaX, deltaY);
    }

    // Scrollable interface methods
    @Override
    public java.awt.Dimension getPreferredScrollableViewportSize() {
        return new java.awt.Dimension(800, 600);
    }

    @Override
    public int getScrollableUnitIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return 10;
    }

    @Override
    public int getScrollableBlockIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return 50;
    }

    @Override
    public boolean getScrollableTracksViewportWidth() {
        return false;
    }

    @Override
    public boolean getScrollableTracksViewportHeight() {
        return false;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=4675	+%0A    // Scrollable interface methods%0A    @Override%0A    public java.awt.Dimension getPreferredScrollableViewportSize() %7B%0A        return new java.awt.Dimension(800, 600);%0A    %7D%0A%0A    @Override%0A    public int getScrollableUnitIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) %7B%0A        return 10;%0A    %7D%0A%0A    @Override%0A    public int getScrollableBlockIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) %7B%0A        return 50;%0A    %7D%0A%0A    @Override%0A    public boolean getScrollableTracksViewportWidth() %7B%0A        return false;%0A    %7D%0A%0A    @Override%0A    public boolean getScrollableTracksViewportHeight() %7B%0A        return false;%0A    %7D%0A	=1
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape, Locatable {
    private String text = "Grail";
    private int xCoordinate, yCoordinate;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public SpeechBubble() {
    }
    @Override
    public String getText() {
        return text;
    }

    @Override
    public void setText(final String newText) {
        final String oldValue = this.text;
        this.text = newText;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "text", oldValue, newText);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return xCoordinate;
    }

    @Override
    public void setX(final int x) {
        final int oldValue = this.xCoordinate;
        this.xCoordinate = x;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return yCoordinate;
    }

    @Override
    public void setY(final int y) {
        final int oldValue = this.yCoordinate;
        this.yCoordinate = y;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=2369	-14	=140
//END OF FILE
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.POINT_PATTERN)
public class APolarPoint implements Point, Locatable, PolarPointInterface {
    private double radius, angle;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public APolarPoint(final double theRadius, final double theAngle) {
        radius = theRadius;
        angle = theAngle;
    }

    public APolarPoint(final int theX, final int theY) {
        radius = Math.sqrt(theX * theX + theY * theY);
        angle = Math.atan((double) theY / theX);
    }

    @Override
    public int getX() {
        return (int) (radius * Math.cos(angle));
    }

    @Override
    public int getY() {
        return (int) (radius * Math.sin(angle));
    }

    @Override
    public double getAngle() {
        return angle;
    }

    @Override
    public double getRadius() {
        return radius;
    }

    @Override
    public void setX(final int x) {
        // Convert to polar coordinates and update
        int currentY = getY();
        radius = Math.sqrt(x * x + currentY * currentY);
        angle = Math.atan2(currentY, x);
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", getX(), x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setY(final int y) {
        // Convert to polar coordinates and update
        int currentX = getX();
        radius = Math.sqrt(currentX * currentX + y * y);
        angle = Math.atan2(y, currentX);
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", getY(), y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=2533	-14	=140
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead extends ImageShape {
    private String fileName = "images/guard.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public GuardHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=318	-4	+BOUND	=1	+D	=1	-1	+SH	=1	-2	+P	=1	-2	=80	-4	=1	-1	+xt	=2	-1	+d	=2741	-14	=145
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import java.beans.PropertyChangeListener;
import java.util.List;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Moveable;
import util.models.PropertyListenerRegisterer;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable, PropertyListenerRegisterer {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }

    private void layoutAtOrigin() {}

    @Override
    public ImageShape getHead() { return head; }

    @Override
    public StringShape getStringShape() { return speech; }

    @Override
    public Angle getArms() { return arms; }

    @Override
    public Angle getLegs() { return legs; }

    @Override
    public void move(final int deltaX, final int deltaY) {
        head.setX(head.getX() + deltaX);
        head.setY(head.getY() + deltaY);
        arms.move(deltaX, deltaY);
        legs.move(deltaX, deltaY);
        speech.setX(speech.getX() + deltaX);
        speech.setY(speech.getY() + deltaY);
        layoutAtOrigin();
    }

    @Override
    public void scroll(final String limb, final int deltaX, final int deltaY) {
        if ("left arm".equalsIgnoreCase(limb)) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + deltaX);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + deltaY);
        } else if ("right arm".equalsIgnoreCase(limb)) {
            arms.getRightLine().setX(arms.getRightLine().getX() + deltaX);
            arms.getRightLine().setY(arms.getRightLine().getY() + deltaY);
        } else if ("left leg".equalsIgnoreCase(limb)) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + deltaX);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + deltaY);
        } else if ("right leg".equalsIgnoreCase(limb)) {
            legs.getRightLine().setX(legs.getRightLine().getX() + deltaX);
            legs.getRightLine().setY(legs.getRightLine().getY() + deltaY);
        }
    }

    public void scale(final double factor) {
        head.setWidth((int) (head.getWidth() * factor));
        head.setHeight((int) (head.getHeight() * factor));
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        head.addPropertyChangeListener(listener);
        speech.addPropertyChangeListener(listener);
        // if arms/legs support property listening:
        arms.getLeftLine().addPropertyChangeListener(listener);
        arms.getRightLine().addPropertyChangeListener(listener);
        legs.getLeftLine().addPropertyChangeListener(listener);
        legs.getRightLine().addPropertyChangeListener(listener);
    }

    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return head.getPropertyChangeListeners(); // or merged list if needed
    }

    // Scrollable interface methods
    @Override
    public java.awt.Dimension getPreferredScrollableViewportSize() {
        return new java.awt.Dimension(200, 300);
    }

    @Override
    public int getScrollableUnitIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return 5;
    }

    @Override
    public int getScrollableBlockIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return 25;
    }

    @Override
    public boolean getScrollableTracksViewportWidth() {
        return false;
    }

    @Override
    public boolean getScrollableTracksViewportHeight() {
        return false;
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=3150	-14	=144	+%0A    %7D%0A%0A    // Scrollable interface methods%0A    @Override%0A    public java.awt.Dimension getPreferredScrollableViewportSize() %7B%0A        return new java.awt.Dimension(200, 300);%0A    %7D%0A%0A    @Override%0A    public int getScrollableUnitIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) %7B%0A        return 5;%0A    %7D%0A%0A    @Override%0A    public int getScrollableBlockIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) %7B%0A        return 25;%0A    %7D%0A%0A    @Override%0A    public boolean getScrollableTracksViewportWidth() %7B%0A        return false;%0A    %7D%0A%0A    @Override%0A    public boolean getScrollableTracksViewportHeight() %7B%0A        return false;	=9
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.ROTATING_LINE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int xOffset, yOffset;
    private static final double UNIT = Math.PI / 32;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public RotatingLine() {
        this.xOffset = 0;
        this.yOffset = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return xOffset + point.getX();
    }

    @Override
    public void setX(final int x) {
        final int oldValue = this.xOffset;
        this.xOffset = x;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return yOffset + point.getY();
    }

    @Override
    public void setY(final int y) {
        final int oldValue = this.yOffset;
        this.yOffset = y;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
    @Override
    public int getWidth() {
        return point.getX();
    }
    @Override
    public int getHeight() {
        return point.getY();
    }

    @Override
    public void setWidth(final int width) {
        // For a rotating line, width doesn't have a direct meaning
        // but we need to implement it for BoundedShape interface
    }

    @Override
    public void setHeight(final int height) {
        // For a rotating line, height doesn't have a direct meaning
        // but we need to implement it for BoundedShape interface
    }
    public double getRadius() {
        return point.getRadius();
    }

    @Override
    public void setRadius(final double radius) {
        point = new APolarPoint(radius, point.getAngle());
    }

    public double getAngle() {
        return point.getAngle();
    }

    @Override
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    @Override
    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override
    public void move(final int deltaX, final int deltaY) {
        setX(xOffset + deltaX);
        setY(yOffset + deltaY);
    }

    // Additional getters/setters for ROTATING_LINE requirements
    public int getXProperty() {
        return getX();
    }

    public void setXProperty(final int x) {
        setX(x);
    }

    public int getYProperty() {
        return getY();
    }

    public void setYProperty(final int y) {
        setY(y);
    }

    public double getRadiusProperty() {
        return getRadius();
    }

    public void setRadiusProperty(final double radius) {
        setRadius(radius);
    }

    public double getAngleProperty() {
        return getAngle();
    }

    public void setAngleProperty(final double angle) {
        setAngle(angle);
    }

    // PropertyListenerRegisterer methods
    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=27	+java.beans.PropertyChangeEvent;%0Aimport java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;%0Aimport 	=389	+%0A    private final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E();	=274	+final int oldValue = this.xOffset;%0A        this.	=12	+%0A        final PropertyChangeEvent event = new PropertyChangeEvent(this, %22x%22, oldValue, x);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=150	+final int oldValue = this.yOffset;%0A        this.	=12	+%0A        final PropertyChangeEvent event = new PropertyChangeEvent(this, %22y%22, oldValue, y);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=162	+%0A	=14	+    public void setWidth(final int width) %7B%0A        // For a rotating line, width doesn't have a direct meaning%0A        // but we need to implement it for BoundedShape interface%0A    %7D%0A%0A    @Override%0A    public void setHeight(final int height) %7B%0A        // For a rotating line, height doesn't have a direct meaning%0A        // but we need to implement it for BoundedShape interface%0A    %7D%0A	=202	-14	=1097	+);%0A    %7D%0A%0A    // PropertyListenerRegisterer methods%0A    @Override%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return new ArrayList%3C%3E(propertyChangeListeners);%0A    %7D%0A%0A    @Override%0A    public void addPropertyChangeListener(final PropertyChangeListener listener) %7B%0A        if (listener != null && !propertyChangeListeners.contains(listener)) %7B%0A            propertyChangeListeners.add(listener);%0A        %7D%0A    %7D%0A%0A    public void removePropertyChangeListener(final PropertyChangeListener listener) %7B%0A        propertyChangeListeners.remove(listener	=11
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface, BoundedShape {
    private int x, y, width, height;
    private final int percentConversion = 100;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public AScalableRectangle(final int initialX, final int initialY, final int initialWidth, final int initialHeight) {
        this.x = initialX;
        this.y = initialY;
        this.width = initialWidth;
        this.height = initialHeight;
    }
    @Override
    public int getX() {
        return x;
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int newHeight) {
        final int oldValue = this.height;
        this.height = newHeight;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, newHeight);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setWidth(final int newWidth) {
        final int oldValue = this.width;
        this.width = newWidth;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, newWidth);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void scale(final int percentage) {
        width = (width * percentage) / percentConversion;
        height = (height * percentage) / percentConversion;
    }

    @Override
    public void setX(final int newX) {
        final int oldValue = this.x;
        this.x = newX;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, newX);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setY(final int newY) {
        final int oldValue = this.y;
        this.y = newY;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, newY);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=3125	-14	=139
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead extends ImageShape {
    private String fileName = "images/robin.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    public RobinHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=318	-4	+BOUND	=1	+D	=1	-1	+SH	=1	-2	+P	=1	-2	=80	-4	=1	-1	+xt	=2	-1	+d	=2740	-14	=145
//END OF FILE

//SESSION END
