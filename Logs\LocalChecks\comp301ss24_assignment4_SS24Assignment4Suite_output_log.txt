
0,Wed Jul 09 20:04:16 EDT 2025*PRE_OUTPUT*
>>Running suite A4Style
<<
>>Running test A4PackageDeclarations
<<
>>Running test AssertingBridgeSceneDynamics
<<
>>Running test A4SimplifyBooleanExpressions
<<
>>Running test A4SimplifyBooleanReturns
<<
>>Running test A4NoHiddenFields
<<
>>Running test A4NamingConventions
<<
>>Running test A4InterfaceAsType
<<
>>Running test A4NamedConstants
<<
>>Running test A4NoStarImports
<<
>>Running test A4PublicMethodsOverride
<<
>>Running test A4MnemonicNames
<<
>>Running test A4Encapsulation
<<
>>Running test A4NonPublicAccessModifiersMatched
<<
>>Running test A4CommonPropertiesAreInherited
<<
>>Running test A4CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
AssertingBridgeSceneDynamics,0.0% complete,0.0,50.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
A4PackageDeclarations,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4SimplifyBooleanExpressions,0.0% complete,0.0,1.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4SimplifyBooleanReturns,0.0% complete,0.0,1.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NoHiddenFields,0.0% complete,0.0,1.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NamingConventions,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4InterfaceAsType,0.0% complete,0.0,7.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NamedConstants,0.0% complete,0.0,3.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NoStarImports,0.0% complete,0.0,1.0,No checkstyle output, check console error messages
<<
>>Test Result:
A4PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4MnemonicNames,0.0% complete,0.0,5.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4Encapsulation,0.0% complete,0.0,3.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4CommonPropertiesAreInherited,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4CommonSignaturesAreInherited,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
*END_OUTPUT*



1,Wed Jul 09 20:13:42 EDT 2025*PRE_OUTPUT*
>>Running suite AbstractClasses
<<
>>Running test TaggedLocatable
<<
>>Running test TaggedBoundedShape
<<
>>Running test AbstractLocatable
<<
>>Running test AbstractBoundedShape
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractLocatable,0.0% complete,0.0,3.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
AbstractBoundedShape,0.0% complete,0.0,3.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
*END_OUTPUT*



1,Wed Jul 09 20:13:44 EDT 2025*PRE_OUTPUT*
>>Running suite Exceptions
<<
>>Running test TaggedImpossibleAngle
<<
>>Running test CheckedImpossibleAngle
<<
>>Running test TaggedLegs
<<
>>Running test TaggedRestrictedLine
<<
>>Running test ArthurIsNotAContortionist
<<
>>Running test BridgeSceneSingletonFromFactory
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractLocatable,0.0% complete,0.0,3.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
AbstractBoundedShape,0.0% complete,0.0,3.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
TaggedImpossibleAngle,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
CheckedImpossibleAngle,0.0% complete,0.0,5.0,
Preceding test TaggedImpossibleAngle failed.
Please correct the problems identified by preceding test:TaggedImpossibleAngle before running this test
<<
>>Test Result:
TaggedLegs,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedRestrictedLine,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
ArthurIsNotAContortionist,0.0% complete,0.0,30.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*



1,Wed Jul 09 20:13:49 EDT 2025*PRE_OUTPUT*
>>Running suite AsyncAnimation
<<
>>Running test AsyncArthurAnimation
<<
>>Running test AbstractionAsyncArthurAnimation: 
Start an animation of arthur and check that a new thread announces prerty changes in response
<<
>>Running test AsyncGalahadAnimation
<<
>>Running test AsyncLancelotAnimation
<<
>>Running test AsyncRobinAnimation
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractLocatable,0.0% complete,0.0,3.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
AbstractBoundedShape,0.0% complete,0.0,3.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
TaggedImpossibleAngle,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
CheckedImpossibleAngle,0.0% complete,0.0,5.0,
Preceding test TaggedImpossibleAngle failed.
Please correct the problems identified by preceding test:TaggedImpossibleAngle before running this test
<<
>>Test Result:
TaggedLegs,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedRestrictedLine,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
ArthurIsNotAContortionist,0.0% complete,0.0,30.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
AsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractionAsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncGalahadAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncLancelotAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncRobinAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
*END_OUTPUT*



1,Wed Jul 09 20:13:52 EDT 2025*PRE_OUTPUT*
>>Running test AssertingBridgeSceneDynamics
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractLocatable,0.0% complete,0.0,3.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
AbstractBoundedShape,0.0% complete,0.0,3.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
TaggedImpossibleAngle,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
CheckedImpossibleAngle,0.0% complete,0.0,5.0,
Preceding test TaggedImpossibleAngle failed.
Please correct the problems identified by preceding test:TaggedImpossibleAngle before running this test
<<
>>Test Result:
TaggedLegs,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedRestrictedLine,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
ArthurIsNotAContortionist,0.0% complete,0.0,30.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
AsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractionAsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncGalahadAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncLancelotAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncRobinAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AssertingBridgeSceneDynamics,0.0% complete,0.0,50.0,No compiled classes found in bin, see transcript for compile errors
<<
*END_OUTPUT*



1,Wed Jul 09 20:14:02 EDT 2025*PRE_OUTPUT*
>>Running suite CoordinatedAnimation
<<
>>Running test BroadcastingClearanceManagerFactoryMethodDefined
<<
>>Running test BroadcastingClearanceManagerSingletonFromFactory
<<
>>Running test WaitingAvatars
<<
>>Running test AbstractionWaitingAvatars: 
Start the waiting animations of 4 knights, execute proceed all, and see if we subsequently get events from all four anumations
<<
>>Running test LockstepAvatars
<<
>>Running test AbstractionLockstepAvatars: 
Start the animation of af Arthur, Lancelot and guard in that order. 
 After the guard is started, wait for property change events from all three avatars to be spaced appropriately in time to show that Arthur and Lancelot are moving to the beat set by the guard
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractLocatable,0.0% complete,0.0,3.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
AbstractBoundedShape,0.0% complete,0.0,3.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
TaggedImpossibleAngle,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
CheckedImpossibleAngle,0.0% complete,0.0,5.0,
Preceding test TaggedImpossibleAngle failed.
Please correct the problems identified by preceding test:TaggedImpossibleAngle before running this test
<<
>>Test Result:
TaggedLegs,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
TaggedRestrictedLine,0.0% complete,0.0,0.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
ArthurIsNotAContortionist,0.0% complete,0.0,30.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
AsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AbstractionAsyncArthurAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncGalahadAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncLancelotAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AsyncRobinAnimation,0.0% complete,0.0,10.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
AssertingBridgeSceneDynamics,0.0% complete,0.0,50.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
BroadcastingClearanceManagerFactoryMethodDefined,0.0% complete,0.0,2.0,No checkstyle output, check console error messages
<<
>>Test Result:
BroadcastingClearanceManagerSingletonFromFactory,0.0% complete,0.0,2.0,
Preceding test BroadcastingClearanceManagerFactoryMethodDefined failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerFactoryMethodDefined before running this test
<<
>>Test Result:
WaitingAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
>>Test Result:
AbstractionWaitingAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
>>Test Result:
LockstepAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
>>Test Result:
AbstractionLockstepAvatars,0.0% complete,0.0,30.0,
Preceding test BroadcastingClearanceManagerSingletonFromFactory failed.
Please correct the problems identified by preceding test:BroadcastingClearanceManagerSingletonFromFactory before running this test
<<
*END_OUTPUT*


