package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X_COORDINATE = 10;
    public static final int SOME_Y_COORDINATE = 50;
    public static final int LANCELOT_CONSTANT = 8;
    public static final int ROBIN_CONSTANT = 15;
    public static final int GALAHAD_CONSTANT = 22;
    public static final int GUARD_CONSTANT = 30;
    private Gorge gorge;
    private Avatar currentAvatar;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X_COORDINATE = 500;
    private static final int KNIGHT_Y_COORDINATE = 600; 
    private static final int GUARD_Y_COORDINATE = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X_COORDINATE = 750;
    private static final int VIEWPORT_WIDTH = 800;
    private static final int VIEWPORT_HEIGHT = 600;
    private static final int UNIT_INCREMENT = 10;
    private static final int BLOCK_INCREMENT = 50;
    private static int gorgeYCoordinate = 0;
    int difference = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X_COORDINATE, SOME_Y_COORDINATE);
      lancelot.move(SOME_X_COORDINATE*LANCELOT_CONSTANT, SOME_Y_COORDINATE);
      robin.move(SOME_X_COORDINATE*ROBIN_CONSTANT, SOME_Y_COORDINATE);
      galahad.move(SOME_X_COORDINATE*GALAHAD_CONSTANT,SOME_Y_COORDINATE);
      guard.move(AREA_X_COORDINATE,GUARD_Y_COORDINATE);
      gorge = new Gorge(GORGE_X_COORDINATE);
      knightArea = new AScalableRectangle(AREA_X_COORDINATE,KNIGHT_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X_COORDINATE,GUARD_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
    }

    // Required getters for BRIDGE_SCENE
    @Override
    public Avatar getArthur() {
        return arthur;
    }

    @Override
    public Avatar getLancelot() {
        return lancelot;
    }

    @Override
    public Avatar getRobin() {
        return robin;
    }

    @Override
    public Avatar getGalahad() {
        return galahad;
    }

    @Override
    public Avatar getGuard() {
        return guard;
    }

    // Additional getters for other properties
    @Override
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }

    @Override
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }

    @Override
    public Gorge getGorge() {
        return gorge;
    }

    @Override
    public boolean getOccupied() {
        return occupied;
    }

    @Override
    public boolean getKnightTurn() {
        return knightTurn;
    }

    @Override
    public void passed() {
    	if(!knightTurn){
    		currentAvatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);
    		occupied = false;
    	}
    }
    @Override
    public void failed() {
        if (!occupied) {
            return;
        }

        if (!knightTurn) {
            currentAvatar.getHead().setX(GORGE_X_COORDINATE);
            currentAvatar.getHead().setY(gorgeYCoordinate);
            gorgeYCoordinate += difference;
            occupied = !occupied;
        } else {
            guard.getHead().setX(GORGE_X_COORDINATE);
            guard.getHead().setY(gorgeYCoordinate);
            gorgeYCoordinate += difference;
        }
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);}
    	occupied = true;
    	currentAvatar = avatar;
    }
    @Override
    public void say(final String speechText) {
        if (!occupied) {
            return;
        }

        if (!knightTurn) {
            guard.getStringShape().setText(speechText);
            knightTurn = !knightTurn;
        } else {
            currentAvatar.getStringShape().setText(speechText);
            knightTurn = !knightTurn;
        }
    }

    @Override
    public void scroll(final String limb, final int deltaX, final int deltaY) {
        currentAvatar.scroll(limb, deltaX, deltaY);
    }

    // Scrollable interface methods
    @Override
    public java.awt.Dimension getPreferredScrollableViewportSize() {
        return new java.awt.Dimension(VIEWPORT_WIDTH, VIEWPORT_HEIGHT);
    }

    @Override
    public int getScrollableUnitIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return UNIT_INCREMENT;
    }

    @Override
    public int getScrollableBlockIncrement(final java.awt.Rectangle visibleRect, final int orientation, final int direction) {
        return BLOCK_INCREMENT;
    }

    @Override
    public boolean getScrollableTracksViewportWidth() {
        return false;
    }

    @Override
    public boolean getScrollableTracksViewportHeight() {
        return false;
    }
}