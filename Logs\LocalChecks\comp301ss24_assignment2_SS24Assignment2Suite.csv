#,Time,%Passes,Change,Test,Pass,Partial,Fail,Untested
0,Tu<PERSON> Jul 08 19:17:59 EDT 2025,0,0,BridgeSceneSemantics, , ,BridgeSceneApproachMethodDefined+ BridgeSceneDynamics+ BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,
1,Tue Jul 08 19:18:01 EDT 2025,0,0,A2Factory, , ,A2MainCallsBridgeSceneFactoryMethod+ AvatarCallsLegFactoryMethod+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ ConsoleSceneViewFactoryMethodDefined+ ConsoleSceneViewSingletonFromFactory+ LegsFactoryMethodDefined+ TaggedFactory+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable ,
2,Tue Jul 08 19:18:02 EDT 2025,0,0,A2ConsoleSceneView, , ,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView+ ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene+ ConsoleSceneViewPrintsPropertyChangeEvent+ ConsoleSceneViewRegistersWithLocatables+ ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedConsoleSceneView+ TaggedFactory TaggedLocatable+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape ,
3,Tue Jul 08 19:18:05 EDT 2025,0,0,A2Observables, , ,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent+ BoundedShapeInstantiatesPropertyChangeEvent+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent+ LocatableInstantiatesPropertyChangeEvent+ LocatablePropertyChangeListenersProperty+ Locatable_IS_A_PropertyListenerRegisterer+ TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
4,Tue Jul 08 19:18:06 EDT 2025,0,0,BridgeSceneScroll, , ,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase+ BridgeSceneArthurScrollRightLegTestCase+ BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase+ BridgeSceneLancelotScrollLeftArmTestCase+ BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase+ BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined+ BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
5,Tue Jul 08 19:18:07 EDT 2025,0,0,A2Style, , ,A2CommonPropertiesAreInherited+ A2CommonSignaturesAreInherited+ A2Encapsulation+ A2InterfaceAsType+ A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames+ A2NamedConstants+ A2NamingConventions+ A2NoHiddenFields+ A2NoStarImports+ A2NonPublicAccessModifiersMatched+ A2PackageDeclarations+ A2PublicMethodsOverride+ A2SimplifyBooleanExpressions+ A2SimplifyBooleanReturns+ AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
6,Tue Jul 08 20:25:34 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
7,Tue Jul 08 20:25:35 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
8,Tue Jul 08 20:25:38 EDT 2025,0,0,BridgeSceneSayMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
9,Tue Jul 08 20:25:39 EDT 2025,0,0,BridgeScenePassedMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
10,Tue Jul 08 20:25:40 EDT 2025,0,0,BridgeSceneFailedMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
11,Tue Jul 08 20:25:40 EDT 2025,0,0,BridgeSceneDynamics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
12,Tue Jul 08 20:25:47 EDT 2025,0,0,BridgeSceneScroll, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
13,Tue Jul 08 20:25:48 EDT 2025,0,0,BridgeSceneScroll, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
14,Tue Jul 08 20:25:50 EDT 2025,0,0,A2Style, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
15,Tue Jul 08 20:25:51 EDT 2025,0,0,A2Observables, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
16,Tue Jul 08 20:25:53 EDT 2025,0,0,A2ConsoleSceneView, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
17,Tue Jul 08 20:25:54 EDT 2025,0,0,TaggedConsoleSceneView, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
18,Tue Jul 08 20:25:59 EDT 2025,0,0,A2Factory, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
19,Tue Jul 08 20:26:00 EDT 2025,0,0,BridgeSceneSingletonFromFactory, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
20,Tue Jul 08 20:26:02 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
21,Tue Jul 08 22:18:48 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
22,Tue Jul 08 22:18:51 EDT 2025,0,0,A2Factory, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
23,Tue Jul 08 22:18:52 EDT 2025,0,0,A2ConsoleSceneView, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
24,Tue Jul 08 22:18:53 EDT 2025,0,0,A2Observables, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
25,Tue Jul 08 22:18:54 EDT 2025,0,0,BridgeSceneScroll, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
26,Tue Jul 08 22:18:56 EDT 2025,0,0,A2Style, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
27,Tue Jul 08 22:19:03 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
28,Tue Jul 08 22:20:15 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
29,Tue Jul 08 22:21:16 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
30,Tue Jul 08 22:22:22 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
31,Tue Jul 08 22:25:47 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
32,Tue Jul 08 22:25:56 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
33,Tue Jul 08 22:29:09 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
34,Tue Jul 08 22:30:08 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
35,Tue Jul 08 22:30:09 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
36,Tue Jul 08 22:32:17 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
37,Tue Jul 08 22:32:20 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
38,Tue Jul 08 22:33:06 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
39,Tue Jul 08 22:33:07 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
40,Tue Jul 08 22:33:08 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
41,Tue Jul 08 22:33:23 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
42,Tue Jul 08 22:34:58 EDT 2025,3,3,BridgeSceneSemantics,BridgeSceneApproachMethodDefined+ BridgeSceneSayMethodDefined+ , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
43,Tue Jul 08 23:04:20 EDT 2025,7,4,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
44,Tue Jul 08 23:06:59 EDT 2025,7,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
45,Tue Jul 08 23:08:51 EDT 2025,7,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
46,Tue Jul 08 23:09:41 EDT 2025,7,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,BridgeSceneDynamics+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
47,Tue Jul 08 23:10:43 EDT 2025,9,2,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneDynamics+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
48,Tue Jul 08 23:11:20 EDT 2025,9,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
49,Tue Jul 08 23:11:27 EDT 2025,20,11,A2Style,A2Encapsulation+ A2NoStarImports+ A2NonPublicAccessModifiersMatched+ A2PackageDeclarations+ A2SimplifyBooleanExpressions+ A2SimplifyBooleanReturns+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited+ A2InterfaceAsType+ A2MnemonicNames+ A2NamedConstants+ A2PublicMethodsOverride+ ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
50,Tue Jul 08 23:11:40 EDT 2025,20,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
51,Tue Jul 08 23:11:45 EDT 2025,20,0,A2ConsoleSceneView,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
52,Tue Jul 08 23:33:23 EDT 2025,20,0,BridgeSceneSemantics,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
53,Wed Jul 09 00:02:11 EDT 2025,20,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
54,Wed Jul 09 00:17:17 EDT 2025,27,7,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ LegsFactoryMethodDefined+ TaggedFactory+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
55,Wed Jul 09 00:24:38 EDT 2025,27,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
56,Wed Jul 09 00:26:04 EDT 2025,29,2,A2ConsoleSceneView,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory TaggedLocatable+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
57,Wed Jul 09 00:26:07 EDT 2025,29,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
58,Wed Jul 09 00:26:12 EDT 2025,29,0,A2MainCallsBridgeSceneFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
59,Wed Jul 09 00:56:15 EDT 2025,25,-4,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined- LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
60,Wed Jul 09 00:56:32 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
61,Wed Jul 09 00:57:24 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
62,Wed Jul 09 00:57:40 EDT 2025,25,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
63,Wed Jul 09 00:57:43 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
64,Wed Jul 09 00:58:35 EDT 2025,23,-2,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory- ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
65,Wed Jul 09 00:58:47 EDT 2025,23,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
66,Wed Jul 09 00:59:27 EDT 2025,25,2,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
67,Wed Jul 09 00:59:32 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
68,Wed Jul 09 00:59:55 EDT 2025,25,0,A2MainCallsBridgeSceneFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
69,Wed Jul 09 00:59:59 EDT 2025,25,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
70,Wed Jul 09 01:00:01 EDT 2025,25,0,LegsFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
71,Wed Jul 09 01:01:09 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewFactoryMethodDefined+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory- ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
72,Wed Jul 09 01:01:25 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined- ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
73,Wed Jul 09 01:02:12 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
74,Wed Jul 09 01:02:15 EDT 2025,25,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
75,Wed Jul 09 01:02:19 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
76,Wed Jul 09 01:20:54 EDT 2025,27,2,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
77,Wed Jul 09 01:21:01 EDT 2025,27,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
78,Wed Jul 09 01:22:02 EDT 2025,27,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
79,Wed Jul 09 01:23:18 EDT 2025,30,3,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined+ ConsoleSceneViewSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
80,Wed Jul 09 01:23:48 EDT 2025,30,0,LegsFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
81,Wed Jul 09 01:25:26 EDT 2025,32,2,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod+ A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
82,Wed Jul 09 01:30:43 EDT 2025,30,-2,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory- LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
83,Wed Jul 09 01:31:38 EDT 2025,30,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
84,Wed Jul 09 01:33:13 EDT 2025,32,2,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
85,Wed Jul 09 01:34:46 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
86,Wed Jul 09 01:36:04 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
87,Wed Jul 09 01:37:53 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
88,Wed Jul 09 01:39:55 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
89,Wed Jul 09 01:41:22 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
90,Wed Jul 09 01:41:25 EDT 2025,32,0,LegsFactoryMethodDefined,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
91,Wed Jul 09 01:43:21 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
92,Wed Jul 09 01:44:49 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
93,Wed Jul 09 01:45:48 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
94,Wed Jul 09 01:48:45 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
95,Wed Jul 09 01:48:47 EDT 2025,32,0,LegsFactoryMethodDefined,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
96,Wed Jul 09 01:52:06 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
97,Wed Jul 09 01:53:48 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
98,Wed Jul 09 01:54:08 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
99,Wed Jul 09 01:54:56 EDT 2025,32,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedConsoleSceneView+ TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedLocatable- ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
100,Wed Jul 09 01:55:01 EDT 2025,34,2,A2Observables,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer+ TaggedConsoleSceneView TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
101,Wed Jul 09 01:55:03 EDT 2025,34,0,BridgeSceneScroll,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
102,Wed Jul 09 01:55:22 EDT 2025,34,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
103,Wed Jul 09 01:58:31 EDT 2025,36,2,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
104,Wed Jul 09 02:06:24 EDT 2025,34,-2,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory- LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
105,Wed Jul 09 02:09:50 EDT 2025,34,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
106,Wed Jul 09 02:11:05 EDT 2025,41,7,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene+ ConsoleSceneViewPrintsPropertyChangeEvent+ ConsoleSceneViewRegistersWithLocatables+ ConsoleSceneViewSingletonFromFactory+ Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView+ ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
107,Wed Jul 09 02:12:04 EDT 2025,41,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
108,Wed Jul 09 02:13:57 EDT 2025,41,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
109,Wed Jul 09 02:15:21 EDT 2025,41,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
110,Wed Jul 09 02:15:28 EDT 2025,41,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
111,Wed Jul 09 02:15:43 EDT 2025,47,6,A2Observables,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent+ LocatableInstantiatesPropertyChangeEvent+ LocatablePropertyChangeListenersProperty+ Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,
112,Wed Jul 09 02:15:51 EDT 2025,54,7,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty+ LocatableXProperty+ LocatableYEditableProperty+ LocatableYProperty+ Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes+ A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty+ BoundedShapeHeightProperty+ BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty+ BoundedShapeWidthProperty+ BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape+ , ,
113,Wed Jul 09 02:16:06 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
114,Wed Jul 09 02:17:22 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
115,Wed Jul 09 02:17:29 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
116,Wed Jul 09 02:19:07 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
117,Wed Jul 09 02:19:10 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
118,Wed Jul 09 02:19:43 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
119,Wed Jul 09 02:19:44 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
120,Wed Jul 09 02:20:10 EDT 2025,63,9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty+ BoundedShapeHeightProperty+ BoundedShapeWidthEditableProperty+ BoundedShapeWidthProperty+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape+ TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,
121,Wed Jul 09 02:20:50 EDT 2025,63,0,A2ExpectedSuperTypes,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,
122,Wed Jul 09 02:21:52 EDT 2025,63,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,
123,Wed Jul 09 02:22:39 EDT 2025,54,-9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty- BoundedShapeHeightProperty- BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty- BoundedShapeWidthProperty- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape- , ,
124,Wed Jul 09 02:22:44 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
125,Wed Jul 09 02:23:00 EDT 2025,63,9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty+ BoundedShapeHeightProperty+ BoundedShapeWidthEditableProperty+ BoundedShapeWidthProperty+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape+ TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,
126,Wed Jul 09 03:21:42 EDT 2025,63,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,
127,Wed Jul 09 03:23:21 EDT 2025,54,-9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty- BoundedShapeHeightProperty- BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty- BoundedShapeWidthProperty- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape- , ,
128,Wed Jul 09 03:31:37 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
129,Wed Jul 09 03:32:33 EDT 2025,45,-9,BridgeSceneSemantics,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneFailedMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined- BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined- BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
130,Wed Jul 09 03:32:53 EDT 2025,34,-11,A2Style,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited- A2Encapsulation- A2ExpectedSuperTypes A2InterfaceAsType- A2MnemonicNames- A2NamedConstants- A2NamingConventions A2NoHiddenFields A2NoStarImports- A2NonPublicAccessModifiersMatched- A2PackageDeclarations- A2PublicMethodsOverride- A2SimplifyBooleanExpressions- A2SimplifyBooleanReturns- AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
131,Wed Jul 09 03:34:41 EDT 2025,40,6,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined+ BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
132,Wed Jul 09 03:37:03 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
133,Wed Jul 09 03:37:07 EDT 2025,40,0,BridgeSceneDynamics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
134,Wed Jul 09 03:37:11 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
135,Wed Jul 09 03:38:20 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
136,Wed Jul 09 03:38:21 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
137,Wed Jul 09 03:38:57 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
138,Wed Jul 09 03:38:59 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
139,Wed Jul 09 03:40:15 EDT 2025,40,0,A2Factory,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
140,Wed Jul 09 03:40:18 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
141,Wed Jul 09 03:40:23 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
142,Wed Jul 09 03:41:24 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
143,Wed Jul 09 03:42:07 EDT 2025,40,0,A2Style,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
144,Wed Jul 09 03:43:50 EDT 2025,40,0,A2Style,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
145,Wed Jul 09 03:43:54 EDT 2025,40,0,A2PackageDeclarations,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
146,Wed Jul 09 03:44:12 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
147,Wed Jul 09 03:48:07 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
148,Wed Jul 09 03:49:38 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
149,Wed Jul 09 03:53:29 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
150,Wed Jul 09 03:56:51 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
151,Wed Jul 09 04:00:23 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
152,Wed Jul 09 04:04:52 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
153,Wed Jul 09 04:08:01 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
154,Wed Jul 09 04:08:13 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
155,Wed Jul 09 04:11:39 EDT 2025,41,1,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
156,Wed Jul 09 04:13:40 EDT 2025,41,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
157,Wed Jul 09 04:15:00 EDT 2025,41,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
158,Wed Jul 09 04:16:17 EDT 2025,43,2,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
159,Wed Jul 09 04:17:28 EDT 2025,40,-3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
160,Wed Jul 09 04:18:04 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
161,Wed Jul 09 04:18:04 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
162,Wed Jul 09 04:18:22 EDT 2025,43,3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneDynamics+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
163,Wed Jul 09 04:18:37 EDT 2025,40,-3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
164,Wed Jul 09 04:18:53 EDT 2025,43,3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneDynamics+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
165,Wed Jul 09 04:21:07 EDT 2025,43,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
166,Wed Jul 09 04:21:17 EDT 2025,52,9,A2Style,A2Encapsulation+ A2MainCallsBridgeSceneFactoryMethod A2NonPublicAccessModifiersMatched+ A2PackageDeclarations+ A2SimplifyBooleanExpressions+ A2SimplifyBooleanReturns+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited+ A2InterfaceAsType+ A2MnemonicNames+ A2NamedConstants+ A2PublicMethodsOverride+ ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields A2NoStarImports AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
167,Wed Jul 09 05:00:04 EDT 2025,52,0,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields A2NoStarImports AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
168,Wed Jul 09 05:00:14 EDT 2025,52,0,A2MnemonicNames,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields A2NoStarImports AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
169,Wed Jul 09 05:22:37 EDT 2025,54,2,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports+ A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
170,Wed Jul 09 05:22:40 EDT 2025,54,0,A2NoHiddenFields,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
171,Wed Jul 09 05:24:52 EDT 2025,56,2,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields+ A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
172,Wed Jul 09 05:25:31 EDT 2025,56,0,A2NamingConventions,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
173,Wed Jul 09 05:29:48 EDT 2025,56,0,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
174,Wed Jul 09 05:29:51 EDT 2025,56,0,A2NamingConventions,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
175,Wed Jul 09 05:31:19 EDT 2025,58,2,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions+ A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
176,Wed Jul 09 05:31:23 EDT 2025,58,0,A2InterfaceAsType,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
177,Wed Jul 09 05:31:28 EDT 2025,58,0,A2NamedConstants,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
178,Wed Jul 09 05:32:19 EDT 2025,58,0,A2PublicMethodsOverride,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
179,Wed Jul 09 05:32:22 EDT 2025,58,0,A2MnemonicNames,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
180,Wed Jul 09 05:33:36 EDT 2025,60,2,A2Style,A2Encapsulation A2InterfaceAsType+ A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
181,Wed Jul 09 05:33:40 EDT 2025,60,0,A2PublicMethodsOverride,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
182,Wed Jul 09 05:35:58 EDT 2025,58,-2,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions- AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
183,Wed Jul 09 05:36:01 EDT 2025,58,0,A2NamingConventions,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
184,Wed Jul 09 05:36:51 EDT 2025,60,2,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions+ A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
185,Wed Jul 09 05:40:36 EDT 2025,60,0,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
186,Wed Jul 09 05:46:29 EDT 2025,60,0,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
187,Wed Jul 09 05:46:30 EDT 2025,60,0,A2PublicMethodsOverride,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
188,Wed Jul 09 05:50:23 EDT 2025,61,1,A2Style,A2CommonSignaturesAreInherited+ A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
189,Wed Jul 09 05:50:32 EDT 2025,61,0,A2PublicMethodsOverride,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
190,Wed Jul 09 05:54:10 EDT 2025,61,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
191,Wed Jul 09 05:54:12 EDT 2025,61,0,A2PublicMethodsOverride,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
192,Wed Jul 09 05:55:41 EDT 2025,63,2,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants+ A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
193,Wed Jul 09 05:59:17 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
194,Wed Jul 09 06:01:20 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
195,Wed Jul 09 06:03:36 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
196,Wed Jul 09 06:10:21 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
197,Wed Jul 09 06:10:25 EDT 2025,63,0,A2MnemonicNames,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
198,Wed Jul 09 06:13:10 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
199,Wed Jul 09 06:13:10 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
200,Wed Jul 09 06:13:16 EDT 2025,63,0,A2PublicMethodsOverride,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
201,Wed Jul 09 06:28:12 EDT 2025,60,-3,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2InterfaceAsType- A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields- AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
202,Wed Jul 09 15:33:39 EDT 2025,60,0,BridgeSceneSemantics,A2CommonSignaturesAreInherited A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
203,Wed Jul 09 15:33:43 EDT 2025,60,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,
204,Wed Jul 09 15:33:50 EDT 2025,49,-11,A2Factory,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedLocatable ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod- A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory- ConsoleSceneViewFactoryMethodDefined- ConsoleSceneViewSingletonFromFactory- LegsFactoryMethodDefined TaggedBoundedShape TaggedFactory- , ,
205,Wed Jul 09 15:33:51 EDT 2025,40,-9,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView- ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene- ConsoleSceneViewPrintsPropertyChangeEvent- ConsoleSceneViewRegistersWithLocatables- ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedBoundedShape TaggedConsoleSceneView- TaggedFactory TaggedLocatable- , ,
206,Wed Jul 09 15:33:54 EDT 2025,32,-8,A2Inheritance,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty- LocatableXProperty- LocatableYEditableProperty- LocatableYProperty- TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
207,Wed Jul 09 15:34:00 EDT 2025,32,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
208,Wed Jul 09 15:41:20 EDT 2025,32,0,BridgeSceneArthurScrollLeftArmTestCase,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
209,Wed Jul 09 15:52:16 EDT 2025,34,2,BridgeSceneScrollMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined+ LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
210,Wed Jul 09 15:53:20 EDT 2025,34,0,BridgeSceneLancelotScrollLeftArmTestCase,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
211,Wed Jul 09 15:58:27 EDT 2025,34,0,TaggedFactory,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
212,Wed Jul 09 15:58:37 EDT 2025,34,0,LegsFactoryMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
213,Wed Jul 09 15:58:39 EDT 2025,34,0,A2MainCallsBridgeSceneFactoryMethod,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
214,Wed Jul 09 15:58:41 EDT 2025,34,0,AvatarCallsLegFactoryMethod,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
215,Wed Jul 09 15:58:48 EDT 2025,34,0,BridgeSceneSemantics,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
216,Wed Jul 09 15:58:48 EDT 2025,34,0,BridgeSceneApproachMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
217,Wed Jul 09 15:59:45 EDT 2025,34,0,LegsFactoryMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
218,Wed Jul 09 18:17:01 EDT 2025,34,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
219,Wed Jul 09 18:17:01 EDT 2025,32,-2,BridgeSceneScroll,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined- BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
220,Wed Jul 09 18:19:43 EDT 2025,30,-2,A2Style,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited- A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
221,Wed Jul 09 18:19:43 EDT 2025,30,0,A2Style,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
222,Wed Jul 09 18:19:48 EDT 2025,30,0,A2Style,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
223,Wed Jul 09 18:19:50 EDT 2025,30,0,A2NoHiddenFields,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
224,Wed Jul 09 18:33:24 EDT 2025,30,0,TaggedFactory,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
225,Wed Jul 09 18:33:31 EDT 2025,30,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
226,Wed Jul 09 18:33:33 EDT 2025,30,0,ConsoleSceneViewFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
227,Wed Jul 09 18:33:35 EDT 2025,30,0,ConsoleSceneViewSingletonFromFactory,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,
228,Wed Jul 09 18:35:17 EDT 2025,32,2,A2Factory,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
229,Wed Jul 09 18:35:25 EDT 2025,32,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
230,Wed Jul 09 18:35:29 EDT 2025,32,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
231,Wed Jul 09 18:45:17 EDT 2025,23,-9,BridgeSceneSemantics,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined- BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined- BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
232,Wed Jul 09 19:06:51 EDT 2025,32,9,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod+ A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod+ BridgeSceneFactoryMethodDefined+ ConsoleSceneViewFactoryMethodDefined+ LegsFactoryMethodDefined+ LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
233,Wed Jul 09 19:06:58 EDT 2025,32,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
234,Wed Jul 09 19:07:01 EDT 2025,27,-5,A2Observables,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent- LocatableInstantiatesPropertyChangeEvent- LocatablePropertyChangeListenersProperty- LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
235,Wed Jul 09 19:07:04 EDT 2025,27,0,BridgeSceneScroll,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
236,Wed Jul 09 19:07:06 EDT 2025,20,-7,A2Style,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined+ ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited- A2Encapsulation- A2ExpectedSuperTypes A2InterfaceAsType- A2MnemonicNames- A2NamedConstants- A2NamingConventions- A2NoHiddenFields A2NoStarImports- A2NonPublicAccessModifiersMatched- A2PackageDeclarations- A2PublicMethodsOverride- A2SimplifyBooleanExpressions- A2SimplifyBooleanReturns- BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
237,Wed Jul 09 19:07:34 EDT 2025,20,0,A2PackageDeclarations,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
238,Wed Jul 09 20:16:18 EDT 2025,20,0,A2Factory,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
239,Wed Jul 09 20:16:18 EDT 2025,20,0,TaggedFactory,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
240,Wed Jul 09 20:16:25 EDT 2025,20,0,A2ConsoleSceneView,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,
