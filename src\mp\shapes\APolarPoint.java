package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.POINT_PATTERN)
public class APolarPoint implements Point, Locatable, PolarPointInterface {
    private double radius, angle;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public APolarPoint(final double theRadius, final double theAngle) {
        radius = theRadius;
        angle = theAngle;
    }

    public APolarPoint(final int theX, final int theY) {
        radius = Math.sqrt(theX * theX + theY * theY);
        angle = Math.atan((double) theY / theX);
    }

    @Override
    public int getX() {
        return (int) (radius * Math.cos(angle));
    }

    @Override
    public int getY() {
        return (int) (radius * Math.sin(angle));
    }

    @Override
    public double getAngle() {
        return angle;
    }

    @Override
    public double getRadius() {
        return radius;
    }

    @Override
    public void setX(final int x) {
        // Convert to polar coordinates and update
        int currentY = getY();
        radius = Math.sqrt(x * x + currentY * currentY);
        angle = Math.atan2(currentY, x);
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", getX(), x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setY(final int y) {
        // Convert to polar coordinates and update
        int currentX = getX();
        radius = Math.sqrt(currentX * currentX + y * y);
        angle = Math.atan2(y, currentX);
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", getY(), y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
