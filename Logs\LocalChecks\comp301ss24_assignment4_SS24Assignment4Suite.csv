#,Time,%Passes,Change,Test,Pass,Partial,Fail,Untested
0,Wed Jul 09 20:04:16 EDT 2025,0,0,A4Style, , ,A4CommonPropertiesAreInherited+ A4CommonSignaturesAreInherited+ A4Encapsulation+ A4InterfaceAsType+ A4MnemonicNames+ A4NamedConstants+ A4NamingConventions+ A4NoHiddenFields+ A4NoStarImports+ A4NonPublicAccessModifiersMatched+ A4PackageDeclarations+ A4PublicMethodsOverride+ A4SimplifyBooleanExpressions+ A4SimplifyBooleanReturns+ AssertingBridgeSceneDynamics+ ,AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars ArthurIsNotAContortionist AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory CheckedImpossibleAngle LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine WaitingAvatars ,
1,Wed Jul 09 20:13:42 EDT 2025,0,0,AbstractClasses, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape+ AbstractLocatable+ AssertingBridgeSceneDynamics TaggedBoundedShape+ TaggedLocatable+ ,AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars ArthurIsNotAContortionist AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory CheckedImpossibleAngle LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation TaggedImpossibleAngle TaggedLegs TaggedRestrictedLine WaitingAvatars ,
2,Wed Jul 09 20:13:44 EDT 2025,0,0,Exceptions, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable ArthurIsNotAContortionist+ AssertingBridgeSceneDynamics BridgeSceneSingletonFromFactory+ CheckedImpossibleAngle+ TaggedBoundedShape TaggedImpossibleAngle+ TaggedLegs+ TaggedLocatable TaggedRestrictedLine+ ,AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation WaitingAvatars ,
3,Wed Jul 09 20:13:49 EDT 2025,0,0,AsyncAnimation, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation+ ArthurIsNotAContortionist AssertingBridgeSceneDynamics AsyncArthurAnimation+ AsyncGalahadAnimation+ AsyncLancelotAnimation+ AsyncRobinAnimation+ BridgeSceneSingletonFromFactory CheckedImpossibleAngle TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine ,AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation WaitingAvatars ,
4,Wed Jul 09 20:13:52 EDT 2025,0,0,AssertingBridgeSceneDynamics, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation ArthurIsNotAContortionist AssertingBridgeSceneDynamics AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory CheckedImpossibleAngle TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine ,AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation WaitingAvatars ,
5,Wed Jul 09 20:14:02 EDT 2025,0,0,CoordinatedAnimation, , ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation AbstractionLockstepAvatars+ AbstractionWaitingAvatars+ ArthurIsNotAContortionist AssertingBridgeSceneDynamics AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined+ BroadcastingClearanceManagerSingletonFromFactory+ CheckedImpossibleAngle LockstepAvatars+ TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine WaitingAvatars+ ,AbstractionSyncArthurAnimation SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation ,
