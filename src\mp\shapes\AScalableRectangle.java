package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface, BoundedShape {
    private int x, y, width, height;
    private final int percentConversion = 100;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public AScalableRectangle(final int initialX, final int initialY, final int initialWidth, final int initialHeight) {
        this.x = initialX;
        this.y = initialY;
        this.width = initialWidth;
        this.height = initialHeight;
    }
    @Override
    public int getX() {
        return x;
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int newHeight) {
        final int oldValue = this.height;
        this.height = newHeight;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, newHeight);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setWidth(final int newWidth) {
        final int oldValue = this.width;
        this.width = newWidth;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, newWidth);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void scale(final int percentage) {
        width = (width * percentage) / percentConversion;
        height = (height * percentage) / percentConversion;
    }

    @Override
    public void setX(final int newX) {
        final int oldValue = this.x;
        this.x = newX;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, newX);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public void setY(final int newY) {
        final int oldValue = this.y;
        this.y = newY;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, newY);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}